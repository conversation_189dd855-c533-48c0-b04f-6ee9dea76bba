package com.example.myapplication.ui.animation

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay

/**
 * 游戏动画组件
 */
object GameAnimations {
    
    /**
     * 卡牌翻转动画
     */
    @Composable
    fun CardFlipAnimation(
        isFlipped: Boolean,
        modifier: Modifier = Modifier,
        frontContent: @Composable () -> Unit,
        backContent: @Composable () -> Unit
    ) {
        val rotation by animateFloatAsState(
            targetValue = if (isFlipped) 180f else 0f,
            animationSpec = tween(durationMillis = 600, easing = FastOutSlowInEasing),
            label = "card_flip"
        )
        
        Box(
            modifier = modifier.graphicsLayer {
                rotationY = rotation
                cameraDistance = 12f * density
            }
        ) {
            if (rotation <= 90f) {
                frontContent()
            } else {
                Box(
                    modifier = Modifier.graphicsLayer {
                        rotationY = 180f
                    }
                ) {
                    backContent()
                }
            }
        }
    }
    
    /**
     * 卡牌出牌动画
     */
    @Composable
    fun CardPlayAnimation(
        isPlaying: Boolean,
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        val scale by animateFloatAsState(
            targetValue = if (isPlaying) 1.1f else 1f,
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessLow
            ),
            label = "card_play_scale"
        )
        
        val offsetY by animateFloatAsState(
            targetValue = if (isPlaying) -20f else 0f,
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessMedium
            ),
            label = "card_play_offset"
        )
        
        Box(
            modifier = modifier
                .scale(scale)
                .graphicsLayer {
                    translationY = offsetY
                }
        ) {
            content()
        }
    }
    
    /**
     * 炸弹爆炸动画
     */
    @Composable
    fun BombExplosionAnimation(
        isExploding: Boolean,
        modifier: Modifier = Modifier,
        onAnimationEnd: () -> Unit = {}
    ) {
        var animationPlayed by remember { mutableStateOf(false) }
        
        val scale by animateFloatAsState(
            targetValue = if (isExploding && !animationPlayed) 3f else 1f,
            animationSpec = tween(durationMillis = 500, easing = FastOutSlowInEasing),
            finishedListener = {
                if (isExploding) {
                    animationPlayed = true
                    onAnimationEnd()
                }
            },
            label = "bomb_explosion_scale"
        )
        
        val alpha by animateFloatAsState(
            targetValue = if (isExploding && !animationPlayed) 0f else 1f,
            animationSpec = tween(durationMillis = 500, delayMillis = 200),
            label = "bomb_explosion_alpha"
        )
        
        val rotation by animateFloatAsState(
            targetValue = if (isExploding && !animationPlayed) 360f else 0f,
            animationSpec = tween(durationMillis = 500, easing = LinearEasing),
            label = "bomb_explosion_rotation"
        )
        
        if (isExploding) {
            Box(
                modifier = modifier
                    .scale(scale)
                    .alpha(alpha)
                    .rotate(rotation),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "💥",
                    fontSize = 48.sp,
                    color = Color.Red
                )
            }
        }
    }
    
    /**
     * 胜利庆祝动画
     */
    @Composable
    fun VictoryAnimation(
        isVisible: Boolean,
        modifier: Modifier = Modifier
    ) {
        AnimatedVisibility(
            visible = isVisible,
            enter = scaleIn(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessLow
                )
            ) + fadeIn(),
            exit = scaleOut() + fadeOut(),
            modifier = modifier
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // 旋转的奖杯
                val rotation by rememberInfiniteTransition(label = "victory_rotation").animateFloat(
                    initialValue = 0f,
                    targetValue = 360f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(durationMillis = 2000, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart
                    ),
                    label = "trophy_rotation"
                )
                
                Text(
                    text = "🏆",
                    fontSize = 64.sp,
                    modifier = Modifier.rotate(rotation)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 闪烁的文字
                val alpha by rememberInfiniteTransition(label = "victory_text_alpha").animateFloat(
                    initialValue = 0.5f,
                    targetValue = 1f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(durationMillis = 1000, easing = FastOutSlowInEasing),
                        repeatMode = RepeatMode.Reverse
                    ),
                    label = "victory_text_alpha"
                )
                
                Text(
                    text = "胜利！",
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFFFFD700),
                    modifier = Modifier.alpha(alpha)
                )
            }
        }
    }
    
    /**
     * 发牌动画
     */
    @Composable
    fun DealingAnimation(
        isDealing: Boolean,
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        val offsetX by animateFloatAsState(
            targetValue = if (isDealing) 0f else -200f,
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessMedium
            ),
            label = "dealing_offset"
        )
        
        val alpha by animateFloatAsState(
            targetValue = if (isDealing) 1f else 0f,
            animationSpec = tween(durationMillis = 300),
            label = "dealing_alpha"
        )
        
        Box(
            modifier = modifier
                .graphicsLayer {
                    translationX = offsetX
                }
                .alpha(alpha)
        ) {
            content()
        }
    }
    
    /**
     * 脉冲动画（用于强调当前玩家）
     */
    @Composable
    fun PulseAnimation(
        isActive: Boolean,
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        val scale by rememberInfiniteTransition(label = "pulse_scale").animateFloat(
            initialValue = 1f,
            targetValue = if (isActive) 1.05f else 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 1000, easing = FastOutSlowInEasing),
                repeatMode = RepeatMode.Reverse
            ),
            label = "pulse_scale_value"
        )
        
        Box(
            modifier = modifier.scale(scale)
        ) {
            content()
        }
    }
    
    /**
     * 摇摆动画（用于警告）
     */
    @Composable
    fun ShakeAnimation(
        isShaking: Boolean,
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        val offsetX by rememberInfiniteTransition(label = "shake_offset").animateFloat(
            initialValue = 0f,
            targetValue = if (isShaking) 10f else 0f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 100, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            ),
            label = "shake_offset_value"
        )
        
        Box(
            modifier = modifier.graphicsLayer {
                translationX = if (isShaking) offsetX else 0f
            }
        ) {
            content()
        }
    }
    
    /**
     * 淡入淡出动画
     */
    @Composable
    fun FadeInOutAnimation(
        isVisible: Boolean,
        modifier: Modifier = Modifier,
        durationMillis: Int = 300,
        content: @Composable () -> Unit
    ) {
        AnimatedVisibility(
            visible = isVisible,
            enter = fadeIn(animationSpec = tween(durationMillis)),
            exit = fadeOut(animationSpec = tween(durationMillis)),
            modifier = modifier
        ) {
            content()
        }
    }
    
    /**
     * 滑动进入动画
     */
    @Composable
    fun SlideInAnimation(
        isVisible: Boolean,
        direction: SlideDirection = SlideDirection.FromBottom,
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        val enterTransition = when (direction) {
            SlideDirection.FromLeft -> slideInHorizontally { -it }
            SlideDirection.FromRight -> slideInHorizontally { it }
            SlideDirection.FromTop -> slideInVertically { -it }
            SlideDirection.FromBottom -> slideInVertically { it }
        }
        
        val exitTransition = when (direction) {
            SlideDirection.FromLeft -> slideOutHorizontally { -it }
            SlideDirection.FromRight -> slideOutHorizontally { it }
            SlideDirection.FromTop -> slideOutVertically { -it }
            SlideDirection.FromBottom -> slideOutVertically { it }
        }
        
        AnimatedVisibility(
            visible = isVisible,
            enter = enterTransition + fadeIn(),
            exit = exitTransition + fadeOut(),
            modifier = modifier
        ) {
            content()
        }
    }
    
    enum class SlideDirection {
        FromLeft, FromRight, FromTop, FromBottom
    }
}
