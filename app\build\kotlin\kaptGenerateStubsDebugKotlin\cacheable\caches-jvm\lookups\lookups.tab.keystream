  R android  raw 	android.R  bomb_explode 
android.R.raw  button_click 
android.R.raw  call 
android.R.raw  	card_flip 
android.R.raw  
card_place 
android.R.raw  deal 
android.R.raw  	game_lose 
android.R.raw  game_win 
android.R.raw  pass 
android.R.raw  shuffle 
android.R.raw  warning 
android.R.raw  Activity android.app  Bundle android.app.Activity  Context android.content  Bundle android.content.Context  	dataStore android.content.Context  getDATAStore android.content.Context  getDataStore android.content.Context  Bundle android.content.ContextWrapper  AudioAttributes 
android.media  AudioManager 
android.media  MediaPlayer 
android.media  	SoundPool 
android.media  Build 
android.os  Bundle 
android.os  
Parcelable 
android.os  VibrationEffect 
android.os  Vibrator 
android.os  VibratorManager 
android.os  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  RawRes androidx.annotation  
Composable androidx.compose.animation  
Composable androidx.compose.animation.core  BorderStroke androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  Card "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  	GameState "androidx.compose.foundation.layout  Player "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Card &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  	GameState &androidx.compose.material.icons.filled  Player &androidx.compose.material.icons.filled  Card androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  	GameState androidx.compose.material3  
MaterialTheme androidx.compose.material3  Player androidx.compose.material3  Surface androidx.compose.material3  
Typography androidx.compose.material3  com androidx.compose.material3  dp androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  sp androidx.compose.material3  Card androidx.compose.runtime  Color androidx.compose.runtime  ColorScheme androidx.compose.runtime  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  	GameState androidx.compose.runtime  Player androidx.compose.runtime  com androidx.compose.runtime  dp androidx.compose.runtime  sp androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  alpha androidx.compose.ui.draw  clip androidx.compose.ui.draw  rotate androidx.compose.ui.draw  scale androidx.compose.ui.draw  Color androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  White "androidx.compose.ui.graphics.Color  White ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  Bundle #androidx.core.app.ComponentActivity  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  AI_DIFFICULTY_1 #androidx.datastore.preferences.core  AI_DIFFICULTY_2 #androidx.datastore.preferences.core  ANIMATION_ENABLED #androidx.datastore.preferences.core  	AUTO_PLAY #androidx.datastore.preferences.core  FIRST_LAUNCH #androidx.datastore.preferences.core  
GAME_SPEED #androidx.datastore.preferences.core  GameSettings #androidx.datastore.preferences.core  
MUSIC_ENABLED #androidx.datastore.preferences.core  PLAYER_NAME #androidx.datastore.preferences.core  
PlayerType #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  
SOUND_ENABLED #androidx.datastore.preferences.core  THEME #androidx.datastore.preferences.core  TUTORIAL_COMPLETED #androidx.datastore.preferences.core  Theme #androidx.datastore.preferences.core  VIBRATION_ENABLED #androidx.datastore.preferences.core  booleanPreferencesKey #androidx.datastore.preferences.core  intPreferencesKey #androidx.datastore.preferences.core  map #androidx.datastore.preferences.core  preferencesDataStore #androidx.datastore.preferences.core  provideDelegate #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Boolean androidx.lifecycle.ViewModel  Card androidx.lifecycle.ViewModel  	CardLogic androidx.lifecycle.ViewModel  CardPattern androidx.lifecycle.ViewModel  
GameEngine androidx.lifecycle.ViewModel  	GameState androidx.lifecycle.ViewModel  GameUiState androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Map androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  Player androidx.lifecycle.ViewModel  
PlayerType androidx.lifecycle.ViewModel  Set androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  emptyMap androidx.lifecycle.ViewModel  emptySet androidx.lifecycle.ViewModel  collectAsStateWithLifecycle androidx.lifecycle.compose  	viewModel $androidx.lifecycle.viewmodel.compose  
Converters 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  GameHistoryEntity 
androidx.room  
GameResult 
androidx.room  GameSettingsEntity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PlayerType 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Volatile 
androidx.room  android 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  GameDatabase androidx.room.RoomDatabase  GameHistoryDao androidx.room.RoomDatabase  GameSettingsDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  android androidx.room.RoomDatabase  gameHistoryDao androidx.room.RoomDatabase  gameSettingsDao androidx.room.RoomDatabase  MainActivity com.example.myapplication  Bundle &com.example.myapplication.MainActivity  Boolean 'com.example.myapplication.data.database  
Converters 'com.example.myapplication.data.database  Dao 'com.example.myapplication.data.database  Delete 'com.example.myapplication.data.database  Double 'com.example.myapplication.data.database  Entity 'com.example.myapplication.data.database  GameDatabase 'com.example.myapplication.data.database  GameHistoryDao 'com.example.myapplication.data.database  GameHistoryEntity 'com.example.myapplication.data.database  
GameResult 'com.example.myapplication.data.database  GameSettingsDao 'com.example.myapplication.data.database  GameSettingsEntity 'com.example.myapplication.data.database  	GameState 'com.example.myapplication.data.database  Insert 'com.example.myapplication.data.database  Int 'com.example.myapplication.data.database  List 'com.example.myapplication.data.database  Long 'com.example.myapplication.data.database  OnConflictStrategy 'com.example.myapplication.data.database  Player 'com.example.myapplication.data.database  
PlayerType 'com.example.myapplication.data.database  
PrimaryKey 'com.example.myapplication.data.database  Query 'com.example.myapplication.data.database  RoomDatabase 'com.example.myapplication.data.database  String 'com.example.myapplication.data.database  
TypeConverter 'com.example.myapplication.data.database  TypeConverters 'com.example.myapplication.data.database  Volatile 'com.example.myapplication.data.database  android 'com.example.myapplication.data.database  
GameResult 2com.example.myapplication.data.database.Converters  
PlayerType 2com.example.myapplication.data.database.Converters  String 2com.example.myapplication.data.database.Converters  
TypeConverter 2com.example.myapplication.data.database.Converters  GameDatabase 4com.example.myapplication.data.database.GameDatabase  GameHistoryDao 4com.example.myapplication.data.database.GameDatabase  GameSettingsDao 4com.example.myapplication.data.database.GameDatabase  Volatile 4com.example.myapplication.data.database.GameDatabase  android 4com.example.myapplication.data.database.GameDatabase  gameHistoryDao 4com.example.myapplication.data.database.GameDatabase  gameSettingsDao 4com.example.myapplication.data.database.GameDatabase  getDatabase 4com.example.myapplication.data.database.GameDatabase  GameDatabase >com.example.myapplication.data.database.GameDatabase.Companion  GameHistoryDao >com.example.myapplication.data.database.GameDatabase.Companion  GameSettingsDao >com.example.myapplication.data.database.GameDatabase.Companion  Volatile >com.example.myapplication.data.database.GameDatabase.Companion  android >com.example.myapplication.data.database.GameDatabase.Companion  getDatabase >com.example.myapplication.data.database.GameDatabase.Companion  Delete 6com.example.myapplication.data.database.GameHistoryDao  Double 6com.example.myapplication.data.database.GameHistoryDao  GameHistoryEntity 6com.example.myapplication.data.database.GameHistoryDao  Insert 6com.example.myapplication.data.database.GameHistoryDao  Int 6com.example.myapplication.data.database.GameHistoryDao  List 6com.example.myapplication.data.database.GameHistoryDao  OnConflictStrategy 6com.example.myapplication.data.database.GameHistoryDao  Query 6com.example.myapplication.data.database.GameHistoryDao  String 6com.example.myapplication.data.database.GameHistoryDao  Boolean 9com.example.myapplication.data.database.GameHistoryEntity  Int 9com.example.myapplication.data.database.GameHistoryEntity  Long 9com.example.myapplication.data.database.GameHistoryEntity  
PrimaryKey 9com.example.myapplication.data.database.GameHistoryEntity  String 9com.example.myapplication.data.database.GameHistoryEntity  Boolean 7com.example.myapplication.data.database.GameSettingsDao  GameSettingsEntity 7com.example.myapplication.data.database.GameSettingsDao  Insert 7com.example.myapplication.data.database.GameSettingsDao  Int 7com.example.myapplication.data.database.GameSettingsDao  OnConflictStrategy 7com.example.myapplication.data.database.GameSettingsDao  Query 7com.example.myapplication.data.database.GameSettingsDao  String 7com.example.myapplication.data.database.GameSettingsDao  Boolean :com.example.myapplication.data.database.GameSettingsEntity  Int :com.example.myapplication.data.database.GameSettingsEntity  
PrimaryKey :com.example.myapplication.data.database.GameSettingsEntity  String :com.example.myapplication.data.database.GameSettingsEntity  AISystem %com.example.myapplication.data.models  Boolean %com.example.myapplication.data.models  Card %com.example.myapplication.data.models  	CardColor %com.example.myapplication.data.models  	CardLogic %com.example.myapplication.data.models  CardPattern %com.example.myapplication.data.models  CardPatternType %com.example.myapplication.data.models  
Comparable %com.example.myapplication.data.models  
Composable %com.example.myapplication.data.models  
Converters %com.example.myapplication.data.models  Dao %com.example.myapplication.data.models  Delete %com.example.myapplication.data.models  Entity %com.example.myapplication.data.models  ExperimentalMaterial3Api %com.example.myapplication.data.models  GameDatabase %com.example.myapplication.data.models  
GameEngine %com.example.myapplication.data.models  GameHistoryEntity %com.example.myapplication.data.models  	GamePhase %com.example.myapplication.data.models  
GameResult %com.example.myapplication.data.models  GameSettingsEntity %com.example.myapplication.data.models  	GameState %com.example.myapplication.data.models  GameUiState %com.example.myapplication.data.models  Insert %com.example.myapplication.data.models  Int %com.example.myapplication.data.models  List %com.example.myapplication.data.models  Long %com.example.myapplication.data.models  MutableList %com.example.myapplication.data.models  MutableStateFlow %com.example.myapplication.data.models  OnConflictStrategy %com.example.myapplication.data.models  Player %com.example.myapplication.data.models  PlayerPosition %com.example.myapplication.data.models  
PlayerRole %com.example.myapplication.data.models  
PlayerType %com.example.myapplication.data.models  
PrimaryKey %com.example.myapplication.data.models  Query %com.example.myapplication.data.models  Rank %com.example.myapplication.data.models  RoomDatabase %com.example.myapplication.data.models  	StateFlow %com.example.myapplication.data.models  String %com.example.myapplication.data.models  Suit %com.example.myapplication.data.models  Triple %com.example.myapplication.data.models  
TypeConverter %com.example.myapplication.data.models  TypeConverters %com.example.myapplication.data.models  Volatile %com.example.myapplication.data.models  android %com.example.myapplication.data.models  asStateFlow %com.example.myapplication.data.models  	emptyList %com.example.myapplication.data.models  emptyMap %com.example.myapplication.data.models  emptySet %com.example.myapplication.data.models  Boolean *com.example.myapplication.data.models.Card  Card *com.example.myapplication.data.models.Card  Int *com.example.myapplication.data.models.Card  List *com.example.myapplication.data.models.Card  Rank *com.example.myapplication.data.models.Card  String *com.example.myapplication.data.models.Card  Suit *com.example.myapplication.data.models.Card  rank *com.example.myapplication.data.models.Card  suit *com.example.myapplication.data.models.Card  Boolean 4com.example.myapplication.data.models.Card.Companion  Card 4com.example.myapplication.data.models.Card.Companion  Int 4com.example.myapplication.data.models.Card.Companion  List 4com.example.myapplication.data.models.Card.Companion  Rank 4com.example.myapplication.data.models.Card.Companion  String 4com.example.myapplication.data.models.Card.Companion  Suit 4com.example.myapplication.data.models.Card.Companion  BLACK /com.example.myapplication.data.models.CardColor  RED /com.example.myapplication.data.models.CardColor  Boolean 1com.example.myapplication.data.models.CardPattern  Card 1com.example.myapplication.data.models.CardPattern  CardPattern 1com.example.myapplication.data.models.CardPattern  CardPatternType 1com.example.myapplication.data.models.CardPattern  Int 1com.example.myapplication.data.models.CardPattern  List 1com.example.myapplication.data.models.CardPattern  Rank 1com.example.myapplication.data.models.CardPattern  mainRank 1com.example.myapplication.data.models.CardPattern  type 1com.example.myapplication.data.models.CardPattern  Boolean ;com.example.myapplication.data.models.CardPattern.Companion  Card ;com.example.myapplication.data.models.CardPattern.Companion  CardPattern ;com.example.myapplication.data.models.CardPattern.Companion  CardPatternType ;com.example.myapplication.data.models.CardPattern.Companion  Int ;com.example.myapplication.data.models.CardPattern.Companion  List ;com.example.myapplication.data.models.CardPattern.Companion  Rank ;com.example.myapplication.data.models.CardPattern.Companion  BOMB 5com.example.myapplication.data.models.CardPatternType  CardPatternType 5com.example.myapplication.data.models.CardPatternType  INVALID 5com.example.myapplication.data.models.CardPatternType  Int 5com.example.myapplication.data.models.CardPatternType  ROCKET 5com.example.myapplication.data.models.CardPatternType  String 5com.example.myapplication.data.models.CardPatternType  equals 5com.example.myapplication.data.models.CardPatternType  priority 5com.example.myapplication.data.models.CardPatternType  CALLING /com.example.myapplication.data.models.GamePhase  FINISHED /com.example.myapplication.data.models.GamePhase  PLAYING /com.example.myapplication.data.models.GamePhase  equals /com.example.myapplication.data.models.GamePhase  Boolean /com.example.myapplication.data.models.GameState  Card /com.example.myapplication.data.models.GameState  CardPattern /com.example.myapplication.data.models.GameState  	GamePhase /com.example.myapplication.data.models.GameState  
GameResult /com.example.myapplication.data.models.GameState  	GameState /com.example.myapplication.data.models.GameState  Int /com.example.myapplication.data.models.GameState  List /com.example.myapplication.data.models.GameState  Long /com.example.myapplication.data.models.GameState  String /com.example.myapplication.data.models.GameState  gameEndTime /com.example.myapplication.data.models.GameState  
gameStartTime /com.example.myapplication.data.models.GameState  
landlordIndex /com.example.myapplication.data.models.GameState  newGame /com.example.myapplication.data.models.GameState  phase /com.example.myapplication.data.models.GameState  Boolean 9com.example.myapplication.data.models.GameState.Companion  Card 9com.example.myapplication.data.models.GameState.Companion  CardPattern 9com.example.myapplication.data.models.GameState.Companion  	GamePhase 9com.example.myapplication.data.models.GameState.Companion  
GameResult 9com.example.myapplication.data.models.GameState.Companion  	GameState 9com.example.myapplication.data.models.GameState.Companion  Int 9com.example.myapplication.data.models.GameState.Companion  List 9com.example.myapplication.data.models.GameState.Companion  Long 9com.example.myapplication.data.models.GameState.Companion  String 9com.example.myapplication.data.models.GameState.Companion  newGame 9com.example.myapplication.data.models.GameState.Companion  Boolean ,com.example.myapplication.data.models.Player  Card ,com.example.myapplication.data.models.Player  Int ,com.example.myapplication.data.models.Player  List ,com.example.myapplication.data.models.Player  MutableList ,com.example.myapplication.data.models.Player  Player ,com.example.myapplication.data.models.Player  PlayerPosition ,com.example.myapplication.data.models.Player  
PlayerRole ,com.example.myapplication.data.models.Player  
PlayerType ,com.example.myapplication.data.models.Player  String ,com.example.myapplication.data.models.Player  hand ,com.example.myapplication.data.models.Player  role ,com.example.myapplication.data.models.Player  type ,com.example.myapplication.data.models.Player  Boolean 6com.example.myapplication.data.models.Player.Companion  Card 6com.example.myapplication.data.models.Player.Companion  Int 6com.example.myapplication.data.models.Player.Companion  List 6com.example.myapplication.data.models.Player.Companion  MutableList 6com.example.myapplication.data.models.Player.Companion  Player 6com.example.myapplication.data.models.Player.Companion  PlayerPosition 6com.example.myapplication.data.models.Player.Companion  
PlayerRole 6com.example.myapplication.data.models.Player.Companion  
PlayerType 6com.example.myapplication.data.models.Player.Companion  String 6com.example.myapplication.data.models.Player.Companion  PlayerPosition 4com.example.myapplication.data.models.PlayerPosition  String 4com.example.myapplication.data.models.PlayerPosition  FARMER 0com.example.myapplication.data.models.PlayerRole  LANDLORD 0com.example.myapplication.data.models.PlayerRole  equals 0com.example.myapplication.data.models.PlayerRole  AI_EASY 0com.example.myapplication.data.models.PlayerType  AI_HARD 0com.example.myapplication.data.models.PlayerType  	AI_MEDIUM 0com.example.myapplication.data.models.PlayerType  HUMAN 0com.example.myapplication.data.models.PlayerType  equals 0com.example.myapplication.data.models.PlayerType  name 0com.example.myapplication.data.models.PlayerType  valueOf 0com.example.myapplication.data.models.PlayerType  	BIG_JOKER *com.example.myapplication.data.models.Rank  Int *com.example.myapplication.data.models.Rank  Rank *com.example.myapplication.data.models.Rank  SMALL_JOKER *com.example.myapplication.data.models.Rank  String *com.example.myapplication.data.models.Rank  equals *com.example.myapplication.data.models.Rank  symbol *com.example.myapplication.data.models.Rank  value *com.example.myapplication.data.models.Rank  	CardColor *com.example.myapplication.data.models.Suit  Int *com.example.myapplication.data.models.Suit  String *com.example.myapplication.data.models.Suit  Suit *com.example.myapplication.data.models.Suit  symbol *com.example.myapplication.data.models.Suit  	CardColor 0com.example.myapplication.data.models.Suit.CLUBS  	CardColor 3com.example.myapplication.data.models.Suit.DIAMONDS  	CardColor 1com.example.myapplication.data.models.Suit.HEARTS  	CardColor 0com.example.myapplication.data.models.Suit.JOKER  	CardColor 1com.example.myapplication.data.models.Suit.SPADES  AI_DIFFICULTY_1 *com.example.myapplication.data.preferences  AI_DIFFICULTY_2 *com.example.myapplication.data.preferences  ANIMATION_ENABLED *com.example.myapplication.data.preferences  	AUTO_PLAY *com.example.myapplication.data.preferences  Boolean *com.example.myapplication.data.preferences  FIRST_LAUNCH *com.example.myapplication.data.preferences  
GAME_SPEED *com.example.myapplication.data.preferences  GameSettings *com.example.myapplication.data.preferences  Int *com.example.myapplication.data.preferences  
MUSIC_ENABLED *com.example.myapplication.data.preferences  PLAYER_NAME *com.example.myapplication.data.preferences  
PlayerType *com.example.myapplication.data.preferences  Preferences *com.example.myapplication.data.preferences  
SOUND_ENABLED *com.example.myapplication.data.preferences  SettingsManager *com.example.myapplication.data.preferences  String *com.example.myapplication.data.preferences  THEME *com.example.myapplication.data.preferences  TUTORIAL_COMPLETED *com.example.myapplication.data.preferences  Theme *com.example.myapplication.data.preferences  VIBRATION_ENABLED *com.example.myapplication.data.preferences  booleanPreferencesKey *com.example.myapplication.data.preferences  intPreferencesKey *com.example.myapplication.data.preferences  map *com.example.myapplication.data.preferences  preferencesDataStore *com.example.myapplication.data.preferences  provideDelegate *com.example.myapplication.data.preferences  stringPreferencesKey *com.example.myapplication.data.preferences  AI_DIFFICULTY_1 :com.example.myapplication.data.preferences.SettingsManager  AI_DIFFICULTY_2 :com.example.myapplication.data.preferences.SettingsManager  ANIMATION_ENABLED :com.example.myapplication.data.preferences.SettingsManager  	AUTO_PLAY :com.example.myapplication.data.preferences.SettingsManager  Boolean :com.example.myapplication.data.preferences.SettingsManager  Context :com.example.myapplication.data.preferences.SettingsManager  	DataStore :com.example.myapplication.data.preferences.SettingsManager  FIRST_LAUNCH :com.example.myapplication.data.preferences.SettingsManager  Flow :com.example.myapplication.data.preferences.SettingsManager  
GAME_SPEED :com.example.myapplication.data.preferences.SettingsManager  GameSettings :com.example.myapplication.data.preferences.SettingsManager  Int :com.example.myapplication.data.preferences.SettingsManager  
MUSIC_ENABLED :com.example.myapplication.data.preferences.SettingsManager  PLAYER_NAME :com.example.myapplication.data.preferences.SettingsManager  
PlayerType :com.example.myapplication.data.preferences.SettingsManager  Preferences :com.example.myapplication.data.preferences.SettingsManager  
SOUND_ENABLED :com.example.myapplication.data.preferences.SettingsManager  String :com.example.myapplication.data.preferences.SettingsManager  THEME :com.example.myapplication.data.preferences.SettingsManager  TUTORIAL_COMPLETED :com.example.myapplication.data.preferences.SettingsManager  Theme :com.example.myapplication.data.preferences.SettingsManager  VIBRATION_ENABLED :com.example.myapplication.data.preferences.SettingsManager  booleanPreferencesKey :com.example.myapplication.data.preferences.SettingsManager  context :com.example.myapplication.data.preferences.SettingsManager  	dataStore :com.example.myapplication.data.preferences.SettingsManager  getMAP :com.example.myapplication.data.preferences.SettingsManager  getMap :com.example.myapplication.data.preferences.SettingsManager  intPreferencesKey :com.example.myapplication.data.preferences.SettingsManager  map :com.example.myapplication.data.preferences.SettingsManager  preferencesDataStore :com.example.myapplication.data.preferences.SettingsManager  provideDelegate :com.example.myapplication.data.preferences.SettingsManager  stringPreferencesKey :com.example.myapplication.data.preferences.SettingsManager  AI_DIFFICULTY_1 Dcom.example.myapplication.data.preferences.SettingsManager.Companion  AI_DIFFICULTY_2 Dcom.example.myapplication.data.preferences.SettingsManager.Companion  ANIMATION_ENABLED Dcom.example.myapplication.data.preferences.SettingsManager.Companion  	AUTO_PLAY Dcom.example.myapplication.data.preferences.SettingsManager.Companion  Boolean Dcom.example.myapplication.data.preferences.SettingsManager.Companion  Context Dcom.example.myapplication.data.preferences.SettingsManager.Companion  	DataStore Dcom.example.myapplication.data.preferences.SettingsManager.Companion  FIRST_LAUNCH Dcom.example.myapplication.data.preferences.SettingsManager.Companion  Flow Dcom.example.myapplication.data.preferences.SettingsManager.Companion  
GAME_SPEED Dcom.example.myapplication.data.preferences.SettingsManager.Companion  GameSettings Dcom.example.myapplication.data.preferences.SettingsManager.Companion  Int Dcom.example.myapplication.data.preferences.SettingsManager.Companion  
MUSIC_ENABLED Dcom.example.myapplication.data.preferences.SettingsManager.Companion  PLAYER_NAME Dcom.example.myapplication.data.preferences.SettingsManager.Companion  
PlayerType Dcom.example.myapplication.data.preferences.SettingsManager.Companion  Preferences Dcom.example.myapplication.data.preferences.SettingsManager.Companion  
SOUND_ENABLED Dcom.example.myapplication.data.preferences.SettingsManager.Companion  String Dcom.example.myapplication.data.preferences.SettingsManager.Companion  THEME Dcom.example.myapplication.data.preferences.SettingsManager.Companion  TUTORIAL_COMPLETED Dcom.example.myapplication.data.preferences.SettingsManager.Companion  Theme Dcom.example.myapplication.data.preferences.SettingsManager.Companion  VIBRATION_ENABLED Dcom.example.myapplication.data.preferences.SettingsManager.Companion  booleanPreferencesKey Dcom.example.myapplication.data.preferences.SettingsManager.Companion  	dataStore Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getBOOLEANPreferencesKey Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getBooleanPreferencesKey Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getINTPreferencesKey Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getIntPreferencesKey Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getMAP Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getMap Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getPREFERENCESDataStore Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getPROVIDEDelegate Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getPreferencesDataStore Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getProvideDelegate Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getSTRINGPreferencesKey Dcom.example.myapplication.data.preferences.SettingsManager.Companion  getStringPreferencesKey Dcom.example.myapplication.data.preferences.SettingsManager.Companion  intPreferencesKey Dcom.example.myapplication.data.preferences.SettingsManager.Companion  map Dcom.example.myapplication.data.preferences.SettingsManager.Companion  preferencesDataStore Dcom.example.myapplication.data.preferences.SettingsManager.Companion  provideDelegate Dcom.example.myapplication.data.preferences.SettingsManager.Companion  stringPreferencesKey Dcom.example.myapplication.data.preferences.SettingsManager.Companion  Boolean )com.example.myapplication.data.repository  Double )com.example.myapplication.data.repository  GameDatabase )com.example.myapplication.data.repository  GameHistoryEntity )com.example.myapplication.data.repository  GameRepository )com.example.myapplication.data.repository  GameSettings )com.example.myapplication.data.repository  	GameState )com.example.myapplication.data.repository  GameStatistics )com.example.myapplication.data.repository  Int )com.example.myapplication.data.repository  List )com.example.myapplication.data.repository  Long )com.example.myapplication.data.repository  Player )com.example.myapplication.data.repository  
PlayerType )com.example.myapplication.data.repository  String )com.example.myapplication.data.repository  Theme )com.example.myapplication.data.repository  Boolean 8com.example.myapplication.data.repository.GameRepository  Context 8com.example.myapplication.data.repository.GameRepository  GameDatabase 8com.example.myapplication.data.repository.GameRepository  GameHistoryEntity 8com.example.myapplication.data.repository.GameRepository  GameSettings 8com.example.myapplication.data.repository.GameRepository  	GameState 8com.example.myapplication.data.repository.GameRepository  GameStatistics 8com.example.myapplication.data.repository.GameRepository  Int 8com.example.myapplication.data.repository.GameRepository  List 8com.example.myapplication.data.repository.GameRepository  Player 8com.example.myapplication.data.repository.GameRepository  
PlayerType 8com.example.myapplication.data.repository.GameRepository  String 8com.example.myapplication.data.repository.GameRepository  Theme 8com.example.myapplication.data.repository.GameRepository  database 8com.example.myapplication.data.repository.GameRepository  Boolean 6com.example.myapplication.data.repository.GameSettings  Int 6com.example.myapplication.data.repository.GameSettings  
PlayerType 6com.example.myapplication.data.repository.GameSettings  String 6com.example.myapplication.data.repository.GameSettings  Theme 6com.example.myapplication.data.repository.GameSettings  Double 8com.example.myapplication.data.repository.GameStatistics  Int 8com.example.myapplication.data.repository.GameStatistics  Long 8com.example.myapplication.data.repository.GameStatistics  AUTO /com.example.myapplication.data.repository.Theme  name /com.example.myapplication.data.repository.Theme  valueOf /com.example.myapplication.data.repository.Theme  AISystem  com.example.myapplication.domain  Any  com.example.myapplication.domain  Boolean  com.example.myapplication.domain  Card  com.example.myapplication.domain  	CardLogic  com.example.myapplication.domain  CardPattern  com.example.myapplication.domain  Double  com.example.myapplication.domain  
GameEngine  com.example.myapplication.domain  	GamePhase  com.example.myapplication.domain  
GameResult  com.example.myapplication.domain  	GameState  com.example.myapplication.domain  GameStateManager  com.example.myapplication.domain  GameUiState  com.example.myapplication.domain  Int  com.example.myapplication.domain  List  com.example.myapplication.domain  Long  com.example.myapplication.domain  Map  com.example.myapplication.domain  MutableStateFlow  com.example.myapplication.domain  Player  com.example.myapplication.domain  
PlayerManager  com.example.myapplication.domain  PlayerPosition  com.example.myapplication.domain  
PlayerRole  com.example.myapplication.domain  
PlayerType  com.example.myapplication.domain  Rank  com.example.myapplication.domain  	StateFlow  com.example.myapplication.domain  String  com.example.myapplication.domain  Triple  com.example.myapplication.domain  asStateFlow  com.example.myapplication.domain  	emptyList  com.example.myapplication.domain  emptyMap  com.example.myapplication.domain  emptySet  com.example.myapplication.domain  Card )com.example.myapplication.domain.AISystem  	CardLogic )com.example.myapplication.domain.AISystem  CardPattern )com.example.myapplication.domain.AISystem  Double )com.example.myapplication.domain.AISystem  	GameState )com.example.myapplication.domain.AISystem  Int )com.example.myapplication.domain.AISystem  List )com.example.myapplication.domain.AISystem  Player )com.example.myapplication.domain.AISystem  Boolean *com.example.myapplication.domain.CardLogic  Card *com.example.myapplication.domain.CardLogic  CardPattern *com.example.myapplication.domain.CardLogic  Int *com.example.myapplication.domain.CardLogic  List *com.example.myapplication.domain.CardLogic  Map *com.example.myapplication.domain.CardLogic  Player *com.example.myapplication.domain.CardLogic  Rank *com.example.myapplication.domain.CardLogic  AISystem +com.example.myapplication.domain.GameEngine  Boolean +com.example.myapplication.domain.GameEngine  Card +com.example.myapplication.domain.GameEngine  	CardLogic +com.example.myapplication.domain.GameEngine  	GameState +com.example.myapplication.domain.GameEngine  Int +com.example.myapplication.domain.GameEngine  List +com.example.myapplication.domain.GameEngine  MutableStateFlow +com.example.myapplication.domain.GameEngine  Player +com.example.myapplication.domain.GameEngine  
PlayerType +com.example.myapplication.domain.GameEngine  	StateFlow +com.example.myapplication.domain.GameEngine  String +com.example.myapplication.domain.GameEngine  
_gameState +com.example.myapplication.domain.GameEngine  _landlordCards +com.example.myapplication.domain.GameEngine  _players +com.example.myapplication.domain.GameEngine  asStateFlow +com.example.myapplication.domain.GameEngine  	emptyList +com.example.myapplication.domain.GameEngine  	gameState +com.example.myapplication.domain.GameEngine  getASStateFlow +com.example.myapplication.domain.GameEngine  getAsStateFlow +com.example.myapplication.domain.GameEngine  getEMPTYList +com.example.myapplication.domain.GameEngine  getEmptyList +com.example.myapplication.domain.GameEngine  
landlordCards +com.example.myapplication.domain.GameEngine  players +com.example.myapplication.domain.GameEngine  Any 1com.example.myapplication.domain.GameStateManager  Boolean 1com.example.myapplication.domain.GameStateManager  Card 1com.example.myapplication.domain.GameStateManager  CardPattern 1com.example.myapplication.domain.GameStateManager  	GamePhase 1com.example.myapplication.domain.GameStateManager  
GameResult 1com.example.myapplication.domain.GameStateManager  	GameState 1com.example.myapplication.domain.GameStateManager  Int 1com.example.myapplication.domain.GameStateManager  List 1com.example.myapplication.domain.GameStateManager  Long 1com.example.myapplication.domain.GameStateManager  Map 1com.example.myapplication.domain.GameStateManager  MutableStateFlow 1com.example.myapplication.domain.GameStateManager  
PlayRecord 1com.example.myapplication.domain.GameStateManager  
PlayerRole 1com.example.myapplication.domain.GameStateManager  	StateFlow 1com.example.myapplication.domain.GameStateManager  String 1com.example.myapplication.domain.GameStateManager  Triple 1com.example.myapplication.domain.GameStateManager  _callingScores 1com.example.myapplication.domain.GameStateManager  
_gameState 1com.example.myapplication.domain.GameStateManager  _playHistory 1com.example.myapplication.domain.GameStateManager  asStateFlow 1com.example.myapplication.domain.GameStateManager  	emptyList 1com.example.myapplication.domain.GameStateManager  emptyMap 1com.example.myapplication.domain.GameStateManager  getASStateFlow 1com.example.myapplication.domain.GameStateManager  getAsStateFlow 1com.example.myapplication.domain.GameStateManager  getEMPTYList 1com.example.myapplication.domain.GameStateManager  getEMPTYMap 1com.example.myapplication.domain.GameStateManager  getEmptyList 1com.example.myapplication.domain.GameStateManager  getEmptyMap 1com.example.myapplication.domain.GameStateManager  Card <com.example.myapplication.domain.GameStateManager.PlayRecord  CardPattern <com.example.myapplication.domain.GameStateManager.PlayRecord  Int <com.example.myapplication.domain.GameStateManager.PlayRecord  List <com.example.myapplication.domain.GameStateManager.PlayRecord  Long <com.example.myapplication.domain.GameStateManager.PlayRecord  String <com.example.myapplication.domain.GameStateManager.PlayRecord  Any .com.example.myapplication.domain.PlayerManager  Boolean .com.example.myapplication.domain.PlayerManager  Card .com.example.myapplication.domain.PlayerManager  Int .com.example.myapplication.domain.PlayerManager  List .com.example.myapplication.domain.PlayerManager  Map .com.example.myapplication.domain.PlayerManager  MutableStateFlow .com.example.myapplication.domain.PlayerManager  Player .com.example.myapplication.domain.PlayerManager  PlayerPosition .com.example.myapplication.domain.PlayerManager  
PlayerRole .com.example.myapplication.domain.PlayerManager  
PlayerType .com.example.myapplication.domain.PlayerManager  	StateFlow .com.example.myapplication.domain.PlayerManager  String .com.example.myapplication.domain.PlayerManager  _currentPlayerIndex .com.example.myapplication.domain.PlayerManager  _landlordIndex .com.example.myapplication.domain.PlayerManager  _players .com.example.myapplication.domain.PlayerManager  asStateFlow .com.example.myapplication.domain.PlayerManager  	emptyList .com.example.myapplication.domain.PlayerManager  getASStateFlow .com.example.myapplication.domain.PlayerManager  getAsStateFlow .com.example.myapplication.domain.PlayerManager  getEMPTYList .com.example.myapplication.domain.PlayerManager  getEmptyList .com.example.myapplication.domain.PlayerManager  Boolean &com.example.myapplication.ui.animation  
Composable &com.example.myapplication.ui.animation  GameAnimations &com.example.myapplication.ui.animation  Int &com.example.myapplication.ui.animation  Unit &com.example.myapplication.ui.animation  Boolean 5com.example.myapplication.ui.animation.GameAnimations  
Composable 5com.example.myapplication.ui.animation.GameAnimations  Int 5com.example.myapplication.ui.animation.GameAnimations  Modifier 5com.example.myapplication.ui.animation.GameAnimations  SlideDirection 5com.example.myapplication.ui.animation.GameAnimations  Unit 5com.example.myapplication.ui.animation.GameAnimations  Boolean 'com.example.myapplication.ui.components  
CallingDialog 'com.example.myapplication.ui.components  Card 'com.example.myapplication.ui.components  
CardComponent 'com.example.myapplication.ui.components  CardSize 'com.example.myapplication.ui.components  CentralPlayArea 'com.example.myapplication.ui.components  
Composable 'com.example.myapplication.ui.components  ExperimentalMaterial3Api 'com.example.myapplication.ui.components  GameOverDialog 'com.example.myapplication.ui.components  	GameState 'com.example.myapplication.ui.components  Int 'com.example.myapplication.ui.components  List 'com.example.myapplication.ui.components  
OpponentAreas 'com.example.myapplication.ui.components  OpponentCard 'com.example.myapplication.ui.components  Player 'com.example.myapplication.ui.components  PlayerHandArea 'com.example.myapplication.ui.components  Set 'com.example.myapplication.ui.components  Unit 'com.example.myapplication.ui.components  Boolean $com.example.myapplication.ui.screens  
Composable $com.example.myapplication.ui.screens  ExperimentalMaterial3Api $com.example.myapplication.ui.screens  GameControlButtons $com.example.myapplication.ui.screens  
GameScreen $com.example.myapplication.ui.screens  	GameState $com.example.myapplication.ui.screens  
GameStatusBar $com.example.myapplication.ui.screens  
LoadingScreen $com.example.myapplication.ui.screens  
MainScreen $com.example.myapplication.ui.screens  OptIn $com.example.myapplication.ui.screens  String $com.example.myapplication.ui.screens  Unit $com.example.myapplication.ui.screens  
WelcomeScreen $com.example.myapplication.ui.screens  Boolean "com.example.myapplication.ui.sound  Float "com.example.myapplication.ui.sound  Int "com.example.myapplication.ui.sound  IntArray "com.example.myapplication.ui.sound  Long "com.example.myapplication.ui.sound  	LongArray "com.example.myapplication.ui.sound  MutableStateFlow "com.example.myapplication.ui.sound  SoundManager "com.example.myapplication.ui.sound  	SoundType "com.example.myapplication.ui.sound  
VibrationType "com.example.myapplication.ui.sound  android "com.example.myapplication.ui.sound  mutableMapOf "com.example.myapplication.ui.sound  Boolean /com.example.myapplication.ui.sound.SoundManager  Context /com.example.myapplication.ui.sound.SoundManager  Float /com.example.myapplication.ui.sound.SoundManager  Int /com.example.myapplication.ui.sound.SoundManager  IntArray /com.example.myapplication.ui.sound.SoundManager  Long /com.example.myapplication.ui.sound.SoundManager  	LongArray /com.example.myapplication.ui.sound.SoundManager  MediaPlayer /com.example.myapplication.ui.sound.SoundManager  MutableStateFlow /com.example.myapplication.ui.sound.SoundManager  RawRes /com.example.myapplication.ui.sound.SoundManager  	SoundPool /com.example.myapplication.ui.sound.SoundManager  	SoundType /com.example.myapplication.ui.sound.SoundManager  	StateFlow /com.example.myapplication.ui.sound.SoundManager  
VibrationType /com.example.myapplication.ui.sound.SoundManager  Vibrator /com.example.myapplication.ui.sound.SoundManager  
_musicEnabled /com.example.myapplication.ui.sound.SoundManager  
_soundEnabled /com.example.myapplication.ui.sound.SoundManager  _vibrationEnabled /com.example.myapplication.ui.sound.SoundManager  android /com.example.myapplication.ui.sound.SoundManager  getMUTABLEMapOf /com.example.myapplication.ui.sound.SoundManager  getMutableMapOf /com.example.myapplication.ui.sound.SoundManager  getVibrator /com.example.myapplication.ui.sound.SoundManager  mutableMapOf /com.example.myapplication.ui.sound.SoundManager  	playSound /com.example.myapplication.ui.sound.SoundManager  vibrate /com.example.myapplication.ui.sound.SoundManager  BOMB_EXPLODE 9com.example.myapplication.ui.sound.SoundManager.SoundType  BUTTON_CLICK 9com.example.myapplication.ui.sound.SoundManager.SoundType  
CALL_LANDLORD 9com.example.myapplication.ui.sound.SoundManager.SoundType  
CARD_PLACE 9com.example.myapplication.ui.sound.SoundManager.SoundType  
DEAL_CARDS 9com.example.myapplication.ui.sound.SoundManager.SoundType  	GAME_LOSE 9com.example.myapplication.ui.sound.SoundManager.SoundType  GAME_WIN 9com.example.myapplication.ui.sound.SoundManager.SoundType  Int 9com.example.myapplication.ui.sound.SoundManager.SoundType  PASS 9com.example.myapplication.ui.sound.SoundManager.SoundType  RawRes 9com.example.myapplication.ui.sound.SoundManager.SoundType  
SHUFFLE_CARDS 9com.example.myapplication.ui.sound.SoundManager.SoundType  	SoundType 9com.example.myapplication.ui.sound.SoundManager.SoundType  WARNING 9com.example.myapplication.ui.sound.SoundManager.SoundType  android 9com.example.myapplication.ui.sound.SoundManager.SoundType  android Fcom.example.myapplication.ui.sound.SoundManager.SoundType.BOMB_EXPLODE  android Fcom.example.myapplication.ui.sound.SoundManager.SoundType.BUTTON_CLICK  android Gcom.example.myapplication.ui.sound.SoundManager.SoundType.CALL_LANDLORD  android Ccom.example.myapplication.ui.sound.SoundManager.SoundType.CARD_FLIP  android Dcom.example.myapplication.ui.sound.SoundManager.SoundType.CARD_PLACE  android Dcom.example.myapplication.ui.sound.SoundManager.SoundType.DEAL_CARDS  android Ccom.example.myapplication.ui.sound.SoundManager.SoundType.GAME_LOSE  android Bcom.example.myapplication.ui.sound.SoundManager.SoundType.GAME_WIN  android >com.example.myapplication.ui.sound.SoundManager.SoundType.PASS  android Gcom.example.myapplication.ui.sound.SoundManager.SoundType.SHUFFLE_CARDS  android Acom.example.myapplication.ui.sound.SoundManager.SoundType.WARNING  BOMB =com.example.myapplication.ui.sound.SoundManager.VibrationType  Int =com.example.myapplication.ui.sound.SoundManager.VibrationType  LIGHT =com.example.myapplication.ui.sound.SoundManager.VibrationType  Long =com.example.myapplication.ui.sound.SoundManager.VibrationType  MEDIUM =com.example.myapplication.ui.sound.SoundManager.VibrationType  STRONG =com.example.myapplication.ui.sound.SoundManager.VibrationType  
VibrationType =com.example.myapplication.ui.sound.SoundManager.VibrationType  Boolean "com.example.myapplication.ui.theme  Color "com.example.myapplication.ui.theme  ColorScheme "com.example.myapplication.ui.theme  
Composable "com.example.myapplication.ui.theme  MyApplicationTheme "com.example.myapplication.ui.theme  Pink40 "com.example.myapplication.ui.theme  Pink80 "com.example.myapplication.ui.theme  Purple40 "com.example.myapplication.ui.theme  Purple80 "com.example.myapplication.ui.theme  PurpleGrey40 "com.example.myapplication.ui.theme  PurpleGrey80 "com.example.myapplication.ui.theme  ThemeManager "com.example.myapplication.ui.theme  
Typography "com.example.myapplication.ui.theme  Unit "com.example.myapplication.ui.theme  com "com.example.myapplication.ui.theme  dp "com.example.myapplication.ui.theme  sp "com.example.myapplication.ui.theme  Color /com.example.myapplication.ui.theme.ThemeManager  ColorScheme /com.example.myapplication.ui.theme.ThemeManager  
Composable /com.example.myapplication.ui.theme.ThemeManager  Theme /com.example.myapplication.ui.theme.ThemeManager  com /com.example.myapplication.ui.theme.ThemeManager  dp /com.example.myapplication.ui.theme.ThemeManager  getDP /com.example.myapplication.ui.theme.ThemeManager  getDp /com.example.myapplication.ui.theme.ThemeManager  getSP /com.example.myapplication.ui.theme.ThemeManager  getSp /com.example.myapplication.ui.theme.ThemeManager  invoke /com.example.myapplication.ui.theme.ThemeManager  sp /com.example.myapplication.ui.theme.ThemeManager  Color :com.example.myapplication.ui.theme.ThemeManager.GameColors  invoke :com.example.myapplication.ui.theme.ThemeManager.GameColors  dp >com.example.myapplication.ui.theme.ThemeManager.GameDimensions  getDP >com.example.myapplication.ui.theme.ThemeManager.GameDimensions  getDp >com.example.myapplication.ui.theme.ThemeManager.GameDimensions  getSP >com.example.myapplication.ui.theme.ThemeManager.GameTypography  getSp >com.example.myapplication.ui.theme.ThemeManager.GameTypography  sp >com.example.myapplication.ui.theme.ThemeManager.GameTypography  Boolean &com.example.myapplication.ui.viewmodel  Card &com.example.myapplication.ui.viewmodel  	CardLogic &com.example.myapplication.ui.viewmodel  CardPattern &com.example.myapplication.ui.viewmodel  
GameEngine &com.example.myapplication.ui.viewmodel  	GameState &com.example.myapplication.ui.viewmodel  GameUiState &com.example.myapplication.ui.viewmodel  
GameViewModel &com.example.myapplication.ui.viewmodel  Int &com.example.myapplication.ui.viewmodel  List &com.example.myapplication.ui.viewmodel  Map &com.example.myapplication.ui.viewmodel  MutableStateFlow &com.example.myapplication.ui.viewmodel  Player &com.example.myapplication.ui.viewmodel  
PlayerType &com.example.myapplication.ui.viewmodel  Set &com.example.myapplication.ui.viewmodel  	StateFlow &com.example.myapplication.ui.viewmodel  String &com.example.myapplication.ui.viewmodel  asStateFlow &com.example.myapplication.ui.viewmodel  	emptyList &com.example.myapplication.ui.viewmodel  emptyMap &com.example.myapplication.ui.viewmodel  emptySet &com.example.myapplication.ui.viewmodel  Boolean 4com.example.myapplication.ui.viewmodel.GameViewModel  Card 4com.example.myapplication.ui.viewmodel.GameViewModel  	CardLogic 4com.example.myapplication.ui.viewmodel.GameViewModel  CardPattern 4com.example.myapplication.ui.viewmodel.GameViewModel  
GameEngine 4com.example.myapplication.ui.viewmodel.GameViewModel  	GameState 4com.example.myapplication.ui.viewmodel.GameViewModel  GameUiState 4com.example.myapplication.ui.viewmodel.GameViewModel  Int 4com.example.myapplication.ui.viewmodel.GameViewModel  List 4com.example.myapplication.ui.viewmodel.GameViewModel  Map 4com.example.myapplication.ui.viewmodel.GameViewModel  MutableStateFlow 4com.example.myapplication.ui.viewmodel.GameViewModel  Player 4com.example.myapplication.ui.viewmodel.GameViewModel  
PlayerType 4com.example.myapplication.ui.viewmodel.GameViewModel  Set 4com.example.myapplication.ui.viewmodel.GameViewModel  	StateFlow 4com.example.myapplication.ui.viewmodel.GameViewModel  String 4com.example.myapplication.ui.viewmodel.GameViewModel  _callingScores 4com.example.myapplication.ui.viewmodel.GameViewModel  _possiblePlays 4com.example.myapplication.ui.viewmodel.GameViewModel  _selectedCards 4com.example.myapplication.ui.viewmodel.GameViewModel  _uiState 4com.example.myapplication.ui.viewmodel.GameViewModel  asStateFlow 4com.example.myapplication.ui.viewmodel.GameViewModel  	emptyList 4com.example.myapplication.ui.viewmodel.GameViewModel  emptyMap 4com.example.myapplication.ui.viewmodel.GameViewModel  emptySet 4com.example.myapplication.ui.viewmodel.GameViewModel  
gameEngine 4com.example.myapplication.ui.viewmodel.GameViewModel  getASStateFlow 4com.example.myapplication.ui.viewmodel.GameViewModel  getAsStateFlow 4com.example.myapplication.ui.viewmodel.GameViewModel  getEMPTYList 4com.example.myapplication.ui.viewmodel.GameViewModel  getEMPTYMap 4com.example.myapplication.ui.viewmodel.GameViewModel  getEMPTYSet 4com.example.myapplication.ui.viewmodel.GameViewModel  getEmptyList 4com.example.myapplication.ui.viewmodel.GameViewModel  getEmptyMap 4com.example.myapplication.ui.viewmodel.GameViewModel  getEmptySet 4com.example.myapplication.ui.viewmodel.GameViewModel  Boolean @com.example.myapplication.ui.viewmodel.GameViewModel.GameUiState  String @com.example.myapplication.ui.viewmodel.GameViewModel.GameUiState  AISystem 	java.lang  AI_DIFFICULTY_1 	java.lang  AI_DIFFICULTY_2 	java.lang  ANIMATION_ENABLED 	java.lang  	AUTO_PLAY 	java.lang  	CardColor 	java.lang  	CardLogic 	java.lang  CardPatternType 	java.lang  Color 	java.lang  
Converters 	java.lang  ExperimentalMaterial3Api 	java.lang  FIRST_LAUNCH 	java.lang  
GAME_SPEED 	java.lang  GameDatabase 	java.lang  
GameEngine 	java.lang  GameHistoryEntity 	java.lang  	GamePhase 	java.lang  GameSettings 	java.lang  GameSettingsEntity 	java.lang  	GameState 	java.lang  GameUiState 	java.lang  Int 	java.lang  
MUSIC_ENABLED 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  PLAYER_NAME 	java.lang  
PlayerRole 	java.lang  
PlayerType 	java.lang  Rank 	java.lang  
SOUND_ENABLED 	java.lang  	SoundType 	java.lang  THEME 	java.lang  TUTORIAL_COMPLETED 	java.lang  Theme 	java.lang  VIBRATION_ENABLED 	java.lang  
VibrationType 	java.lang  android 	java.lang  asStateFlow 	java.lang  booleanPreferencesKey 	java.lang  com 	java.lang  dp 	java.lang  	emptyList 	java.lang  emptyMap 	java.lang  emptySet 	java.lang  intPreferencesKey 	java.lang  map 	java.lang  mutableMapOf 	java.lang  preferencesDataStore 	java.lang  provideDelegate 	java.lang  sp 	java.lang  stringPreferencesKey 	java.lang  AISystem kotlin  AI_DIFFICULTY_1 kotlin  AI_DIFFICULTY_2 kotlin  ANIMATION_ENABLED kotlin  	AUTO_PLAY kotlin  Any kotlin  Array kotlin  Boolean kotlin  	CardColor kotlin  	CardLogic kotlin  CardPatternType kotlin  Color kotlin  
Comparable kotlin  
Converters kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  FIRST_LAUNCH kotlin  Float kotlin  
GAME_SPEED kotlin  GameDatabase kotlin  
GameEngine kotlin  GameHistoryEntity kotlin  	GamePhase kotlin  GameSettings kotlin  GameSettingsEntity kotlin  	GameState kotlin  GameUiState kotlin  Int kotlin  IntArray kotlin  Long kotlin  	LongArray kotlin  
MUSIC_ENABLED kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  PLAYER_NAME kotlin  
PlayerRole kotlin  
PlayerType kotlin  Rank kotlin  
SOUND_ENABLED kotlin  	SoundType kotlin  String kotlin  THEME kotlin  TUTORIAL_COMPLETED kotlin  Theme kotlin  Triple kotlin  Unit kotlin  VIBRATION_ENABLED kotlin  
VibrationType kotlin  Volatile kotlin  android kotlin  arrayOf kotlin  asStateFlow kotlin  booleanPreferencesKey kotlin  com kotlin  dp kotlin  	emptyList kotlin  emptyMap kotlin  emptySet kotlin  intPreferencesKey kotlin  map kotlin  mutableMapOf kotlin  preferencesDataStore kotlin  provideDelegate kotlin  sp kotlin  stringPreferencesKey kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  
getANDROID kotlin.Enum.Companion  
getAndroid kotlin.Enum.Companion  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  AISystem kotlin.annotation  AI_DIFFICULTY_1 kotlin.annotation  AI_DIFFICULTY_2 kotlin.annotation  ANIMATION_ENABLED kotlin.annotation  	AUTO_PLAY kotlin.annotation  	CardColor kotlin.annotation  	CardLogic kotlin.annotation  CardPatternType kotlin.annotation  Color kotlin.annotation  
Converters kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  FIRST_LAUNCH kotlin.annotation  
GAME_SPEED kotlin.annotation  GameDatabase kotlin.annotation  
GameEngine kotlin.annotation  GameHistoryEntity kotlin.annotation  	GamePhase kotlin.annotation  GameSettings kotlin.annotation  GameSettingsEntity kotlin.annotation  	GameState kotlin.annotation  GameUiState kotlin.annotation  Int kotlin.annotation  
MUSIC_ENABLED kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  PLAYER_NAME kotlin.annotation  
PlayerRole kotlin.annotation  
PlayerType kotlin.annotation  Rank kotlin.annotation  
SOUND_ENABLED kotlin.annotation  	SoundType kotlin.annotation  THEME kotlin.annotation  TUTORIAL_COMPLETED kotlin.annotation  Theme kotlin.annotation  Triple kotlin.annotation  VIBRATION_ENABLED kotlin.annotation  
VibrationType kotlin.annotation  Volatile kotlin.annotation  android kotlin.annotation  asStateFlow kotlin.annotation  booleanPreferencesKey kotlin.annotation  com kotlin.annotation  dp kotlin.annotation  	emptyList kotlin.annotation  emptyMap kotlin.annotation  emptySet kotlin.annotation  intPreferencesKey kotlin.annotation  map kotlin.annotation  mutableMapOf kotlin.annotation  preferencesDataStore kotlin.annotation  provideDelegate kotlin.annotation  sp kotlin.annotation  stringPreferencesKey kotlin.annotation  AISystem kotlin.collections  AI_DIFFICULTY_1 kotlin.collections  AI_DIFFICULTY_2 kotlin.collections  ANIMATION_ENABLED kotlin.collections  	AUTO_PLAY kotlin.collections  	CardColor kotlin.collections  	CardLogic kotlin.collections  CardPatternType kotlin.collections  Color kotlin.collections  
Converters kotlin.collections  ExperimentalMaterial3Api kotlin.collections  FIRST_LAUNCH kotlin.collections  
GAME_SPEED kotlin.collections  GameDatabase kotlin.collections  
GameEngine kotlin.collections  GameHistoryEntity kotlin.collections  	GamePhase kotlin.collections  GameSettings kotlin.collections  GameSettingsEntity kotlin.collections  	GameState kotlin.collections  GameUiState kotlin.collections  Int kotlin.collections  List kotlin.collections  
MUSIC_ENABLED kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  PLAYER_NAME kotlin.collections  
PlayerRole kotlin.collections  
PlayerType kotlin.collections  Rank kotlin.collections  
SOUND_ENABLED kotlin.collections  Set kotlin.collections  	SoundType kotlin.collections  THEME kotlin.collections  TUTORIAL_COMPLETED kotlin.collections  Theme kotlin.collections  Triple kotlin.collections  VIBRATION_ENABLED kotlin.collections  
VibrationType kotlin.collections  Volatile kotlin.collections  android kotlin.collections  asStateFlow kotlin.collections  booleanPreferencesKey kotlin.collections  com kotlin.collections  dp kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  emptySet kotlin.collections  intPreferencesKey kotlin.collections  map kotlin.collections  mutableMapOf kotlin.collections  preferencesDataStore kotlin.collections  provideDelegate kotlin.collections  sp kotlin.collections  stringPreferencesKey kotlin.collections  AISystem kotlin.comparisons  AI_DIFFICULTY_1 kotlin.comparisons  AI_DIFFICULTY_2 kotlin.comparisons  ANIMATION_ENABLED kotlin.comparisons  	AUTO_PLAY kotlin.comparisons  	CardColor kotlin.comparisons  	CardLogic kotlin.comparisons  CardPatternType kotlin.comparisons  Color kotlin.comparisons  
Converters kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  FIRST_LAUNCH kotlin.comparisons  
GAME_SPEED kotlin.comparisons  GameDatabase kotlin.comparisons  
GameEngine kotlin.comparisons  GameHistoryEntity kotlin.comparisons  	GamePhase kotlin.comparisons  GameSettings kotlin.comparisons  GameSettingsEntity kotlin.comparisons  	GameState kotlin.comparisons  GameUiState kotlin.comparisons  Int kotlin.comparisons  
MUSIC_ENABLED kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  PLAYER_NAME kotlin.comparisons  
PlayerRole kotlin.comparisons  
PlayerType kotlin.comparisons  Rank kotlin.comparisons  
SOUND_ENABLED kotlin.comparisons  	SoundType kotlin.comparisons  THEME kotlin.comparisons  TUTORIAL_COMPLETED kotlin.comparisons  Theme kotlin.comparisons  Triple kotlin.comparisons  VIBRATION_ENABLED kotlin.comparisons  
VibrationType kotlin.comparisons  Volatile kotlin.comparisons  android kotlin.comparisons  asStateFlow kotlin.comparisons  booleanPreferencesKey kotlin.comparisons  com kotlin.comparisons  dp kotlin.comparisons  	emptyList kotlin.comparisons  emptyMap kotlin.comparisons  emptySet kotlin.comparisons  intPreferencesKey kotlin.comparisons  map kotlin.comparisons  mutableMapOf kotlin.comparisons  preferencesDataStore kotlin.comparisons  provideDelegate kotlin.comparisons  sp kotlin.comparisons  stringPreferencesKey kotlin.comparisons  SuspendFunction1 kotlin.coroutines  AISystem 	kotlin.io  AI_DIFFICULTY_1 	kotlin.io  AI_DIFFICULTY_2 	kotlin.io  ANIMATION_ENABLED 	kotlin.io  	AUTO_PLAY 	kotlin.io  	CardColor 	kotlin.io  	CardLogic 	kotlin.io  CardPatternType 	kotlin.io  Color 	kotlin.io  
Converters 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  FIRST_LAUNCH 	kotlin.io  
GAME_SPEED 	kotlin.io  GameDatabase 	kotlin.io  
GameEngine 	kotlin.io  GameHistoryEntity 	kotlin.io  	GamePhase 	kotlin.io  GameSettings 	kotlin.io  GameSettingsEntity 	kotlin.io  	GameState 	kotlin.io  GameUiState 	kotlin.io  Int 	kotlin.io  
MUSIC_ENABLED 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  PLAYER_NAME 	kotlin.io  
PlayerRole 	kotlin.io  
PlayerType 	kotlin.io  Rank 	kotlin.io  
SOUND_ENABLED 	kotlin.io  	SoundType 	kotlin.io  THEME 	kotlin.io  TUTORIAL_COMPLETED 	kotlin.io  Theme 	kotlin.io  Triple 	kotlin.io  VIBRATION_ENABLED 	kotlin.io  
VibrationType 	kotlin.io  Volatile 	kotlin.io  android 	kotlin.io  asStateFlow 	kotlin.io  booleanPreferencesKey 	kotlin.io  com 	kotlin.io  dp 	kotlin.io  	emptyList 	kotlin.io  emptyMap 	kotlin.io  emptySet 	kotlin.io  intPreferencesKey 	kotlin.io  map 	kotlin.io  mutableMapOf 	kotlin.io  preferencesDataStore 	kotlin.io  provideDelegate 	kotlin.io  sp 	kotlin.io  stringPreferencesKey 	kotlin.io  AISystem 
kotlin.jvm  AI_DIFFICULTY_1 
kotlin.jvm  AI_DIFFICULTY_2 
kotlin.jvm  ANIMATION_ENABLED 
kotlin.jvm  	AUTO_PLAY 
kotlin.jvm  	CardColor 
kotlin.jvm  	CardLogic 
kotlin.jvm  CardPatternType 
kotlin.jvm  Color 
kotlin.jvm  
Converters 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  FIRST_LAUNCH 
kotlin.jvm  
GAME_SPEED 
kotlin.jvm  GameDatabase 
kotlin.jvm  
GameEngine 
kotlin.jvm  GameHistoryEntity 
kotlin.jvm  	GamePhase 
kotlin.jvm  GameSettings 
kotlin.jvm  GameSettingsEntity 
kotlin.jvm  	GameState 
kotlin.jvm  GameUiState 
kotlin.jvm  Int 
kotlin.jvm  
MUSIC_ENABLED 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  PLAYER_NAME 
kotlin.jvm  
PlayerRole 
kotlin.jvm  
PlayerType 
kotlin.jvm  Rank 
kotlin.jvm  
SOUND_ENABLED 
kotlin.jvm  	SoundType 
kotlin.jvm  THEME 
kotlin.jvm  TUTORIAL_COMPLETED 
kotlin.jvm  Theme 
kotlin.jvm  Triple 
kotlin.jvm  VIBRATION_ENABLED 
kotlin.jvm  
VibrationType 
kotlin.jvm  Volatile 
kotlin.jvm  android 
kotlin.jvm  asStateFlow 
kotlin.jvm  booleanPreferencesKey 
kotlin.jvm  com 
kotlin.jvm  dp 
kotlin.jvm  	emptyList 
kotlin.jvm  emptyMap 
kotlin.jvm  emptySet 
kotlin.jvm  intPreferencesKey 
kotlin.jvm  map 
kotlin.jvm  mutableMapOf 
kotlin.jvm  preferencesDataStore 
kotlin.jvm  provideDelegate 
kotlin.jvm  sp 
kotlin.jvm  stringPreferencesKey 
kotlin.jvm  ReadOnlyProperty kotlin.properties  getPROVIDEDelegate "kotlin.properties.ReadOnlyProperty  getProvideDelegate "kotlin.properties.ReadOnlyProperty  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  Random 
kotlin.random  AISystem 
kotlin.ranges  AI_DIFFICULTY_1 
kotlin.ranges  AI_DIFFICULTY_2 
kotlin.ranges  ANIMATION_ENABLED 
kotlin.ranges  	AUTO_PLAY 
kotlin.ranges  	CardColor 
kotlin.ranges  	CardLogic 
kotlin.ranges  CardPatternType 
kotlin.ranges  Color 
kotlin.ranges  
Converters 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  FIRST_LAUNCH 
kotlin.ranges  
GAME_SPEED 
kotlin.ranges  GameDatabase 
kotlin.ranges  
GameEngine 
kotlin.ranges  GameHistoryEntity 
kotlin.ranges  	GamePhase 
kotlin.ranges  GameSettings 
kotlin.ranges  GameSettingsEntity 
kotlin.ranges  	GameState 
kotlin.ranges  GameUiState 
kotlin.ranges  Int 
kotlin.ranges  
MUSIC_ENABLED 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  PLAYER_NAME 
kotlin.ranges  
PlayerRole 
kotlin.ranges  
PlayerType 
kotlin.ranges  Rank 
kotlin.ranges  
SOUND_ENABLED 
kotlin.ranges  	SoundType 
kotlin.ranges  THEME 
kotlin.ranges  TUTORIAL_COMPLETED 
kotlin.ranges  Theme 
kotlin.ranges  Triple 
kotlin.ranges  VIBRATION_ENABLED 
kotlin.ranges  
VibrationType 
kotlin.ranges  Volatile 
kotlin.ranges  android 
kotlin.ranges  asStateFlow 
kotlin.ranges  booleanPreferencesKey 
kotlin.ranges  com 
kotlin.ranges  dp 
kotlin.ranges  	emptyList 
kotlin.ranges  emptyMap 
kotlin.ranges  emptySet 
kotlin.ranges  intPreferencesKey 
kotlin.ranges  map 
kotlin.ranges  mutableMapOf 
kotlin.ranges  preferencesDataStore 
kotlin.ranges  provideDelegate 
kotlin.ranges  sp 
kotlin.ranges  stringPreferencesKey 
kotlin.ranges  KClass kotlin.reflect  AISystem kotlin.sequences  AI_DIFFICULTY_1 kotlin.sequences  AI_DIFFICULTY_2 kotlin.sequences  ANIMATION_ENABLED kotlin.sequences  	AUTO_PLAY kotlin.sequences  	CardColor kotlin.sequences  	CardLogic kotlin.sequences  CardPatternType kotlin.sequences  Color kotlin.sequences  
Converters kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  FIRST_LAUNCH kotlin.sequences  
GAME_SPEED kotlin.sequences  GameDatabase kotlin.sequences  
GameEngine kotlin.sequences  GameHistoryEntity kotlin.sequences  	GamePhase kotlin.sequences  GameSettings kotlin.sequences  GameSettingsEntity kotlin.sequences  	GameState kotlin.sequences  GameUiState kotlin.sequences  Int kotlin.sequences  
MUSIC_ENABLED kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  PLAYER_NAME kotlin.sequences  
PlayerRole kotlin.sequences  
PlayerType kotlin.sequences  Rank kotlin.sequences  
SOUND_ENABLED kotlin.sequences  	SoundType kotlin.sequences  THEME kotlin.sequences  TUTORIAL_COMPLETED kotlin.sequences  Theme kotlin.sequences  Triple kotlin.sequences  VIBRATION_ENABLED kotlin.sequences  
VibrationType kotlin.sequences  Volatile kotlin.sequences  android kotlin.sequences  asStateFlow kotlin.sequences  booleanPreferencesKey kotlin.sequences  com kotlin.sequences  dp kotlin.sequences  	emptyList kotlin.sequences  emptyMap kotlin.sequences  emptySet kotlin.sequences  intPreferencesKey kotlin.sequences  map kotlin.sequences  mutableMapOf kotlin.sequences  preferencesDataStore kotlin.sequences  provideDelegate kotlin.sequences  sp kotlin.sequences  stringPreferencesKey kotlin.sequences  AISystem kotlin.text  AI_DIFFICULTY_1 kotlin.text  AI_DIFFICULTY_2 kotlin.text  ANIMATION_ENABLED kotlin.text  	AUTO_PLAY kotlin.text  	CardColor kotlin.text  	CardLogic kotlin.text  CardPatternType kotlin.text  Color kotlin.text  
Converters kotlin.text  ExperimentalMaterial3Api kotlin.text  FIRST_LAUNCH kotlin.text  
GAME_SPEED kotlin.text  GameDatabase kotlin.text  
GameEngine kotlin.text  GameHistoryEntity kotlin.text  	GamePhase kotlin.text  GameSettings kotlin.text  GameSettingsEntity kotlin.text  	GameState kotlin.text  GameUiState kotlin.text  Int kotlin.text  
MUSIC_ENABLED kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  PLAYER_NAME kotlin.text  
PlayerRole kotlin.text  
PlayerType kotlin.text  Rank kotlin.text  
SOUND_ENABLED kotlin.text  	SoundType kotlin.text  THEME kotlin.text  TUTORIAL_COMPLETED kotlin.text  Theme kotlin.text  Triple kotlin.text  VIBRATION_ENABLED kotlin.text  
VibrationType kotlin.text  Volatile kotlin.text  android kotlin.text  asStateFlow kotlin.text  booleanPreferencesKey kotlin.text  com kotlin.text  dp kotlin.text  	emptyList kotlin.text  emptyMap kotlin.text  emptySet kotlin.text  intPreferencesKey kotlin.text  map kotlin.text  mutableMapOf kotlin.text  preferencesDataStore kotlin.text  provideDelegate kotlin.text  sp kotlin.text  stringPreferencesKey kotlin.text  delay kotlinx.coroutines  launch kotlinx.coroutines  Card kotlinx.coroutines.flow  	CardLogic kotlinx.coroutines.flow  CardPattern kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  
GameEngine kotlinx.coroutines.flow  	GameState kotlinx.coroutines.flow  GameUiState kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  Player kotlinx.coroutines.flow  
PlayerType kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  emptyMap kotlinx.coroutines.flow  emptySet kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  map kotlinx.coroutines.flow  getMAP kotlinx.coroutines.flow.Flow  getMap kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  asStateFlow !kotlinx.coroutines.flow.StateFlow  getASStateFlow !kotlinx.coroutines.flow.StateFlow  getAsStateFlow !kotlinx.coroutines.flow.StateFlow  	Parcelize kotlinx.parcelize  decodeFromString kotlinx.serialization  encodeToString kotlinx.serialization  Json kotlinx.serialization.json                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                