package com.example.myapplication.data.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 玩家类型枚举
 */
enum class PlayerType {
    HUMAN,      // 人类玩家
    AI_EASY,    // 简单AI
    AI_MEDIUM,  // 中等AI
    AI_HARD     // 困难AI
}

/**
 * 玩家角色枚举
 */
enum class PlayerRole {
    LANDLORD,   // 地主
    FARMER      // 农民
}

/**
 * 玩家位置枚举
 */
enum class PlayerPosition(val displayName: String) {
    BOTTOM("下家"),     // 玩家自己
    LEFT("左家"),       // 左边对手
    RIGHT("右家")       // 右边对手
}

/**
 * 玩家数据类
 */
@Parcelize
data class Player(
    val id: String,
    val name: String,
    val type: PlayerType,
    val position: PlayerPosition,
    var role: PlayerRole? = null,
    var hand: MutableList<Card> = mutableListOf(),
    var playedCards: MutableList<List<Card>> = mutableListOf(),
    var score: Int = 0,
    var isActive: Boolean = false,
    var hasCalledLandlord: Boolean = false,
    var passCount: Int = 0
) : Parcelable {
    
    /**
     * 获取手牌数量
     */
    val handSize: Int
        get() = hand.size
    
    /**
     * 是否为地主
     */
    val isLandlord: Boolean
        get() = role == PlayerRole.LANDLORD
    
    /**
     * 是否为农民
     */
    val isFarmer: Boolean
        get() = role == PlayerRole.FARMER
    
    /**
     * 是否为人类玩家
     */
    val isHuman: Boolean
        get() = type == PlayerType.HUMAN
    
    /**
     * 是否为AI玩家
     */
    val isAI: Boolean
        get() = type != PlayerType.HUMAN
    
    /**
     * 添加牌到手牌
     */
    fun addCards(cards: List<Card>) {
        hand.addAll(cards)
        sortHand()
    }
    
    /**
     * 添加单张牌到手牌
     */
    fun addCard(card: Card) {
        hand.add(card)
        sortHand()
    }
    
    /**
     * 从手牌中移除指定牌
     */
    fun removeCards(cards: List<Card>): Boolean {
        return if (hand.containsAll(cards)) {
            hand.removeAll(cards.toSet())
            true
        } else {
            false
        }
    }
    
    /**
     * 出牌
     */
    fun playCards(cards: List<Card>): Boolean {
        return if (removeCards(cards)) {
            playedCards.add(cards)
            true
        } else {
            false
        }
    }
    
    /**
     * 整理手牌（按权重排序）
     */
    fun sortHand() {
        hand.sortBy { it.weight }
    }
    
    /**
     * 清空手牌
     */
    fun clearHand() {
        hand.clear()
        playedCards.clear()
    }
    
    /**
     * 重置玩家状态
     */
    fun reset() {
        role = null
        clearHand()
        score = 0
        isActive = false
        hasCalledLandlord = false
        passCount = 0
    }

    /**
     * 获取玩家统计信息
     */
    fun getPlayerStats(): Map<String, Any> {
        return mapOf(
            "name" to name,
            "type" to type.name,
            "handSize" to handSize,
            "role" to (role?.name ?: "NONE"),
            "score" to score,
            "playCount" to playedCards.size,
            "passCount" to passCount,
            "hasCalledLandlord" to hasCalledLandlord
        )
    }

    /**
     * 检查是否有指定牌型
     */
    fun hasPattern(pattern: CardPatternType): Boolean {
        return when (pattern) {
            CardPatternType.BOMB -> {
                val rankGroups = hand.groupBy { it.rank }
                rankGroups.any { it.value.size == 4 }
            }
            CardPatternType.ROCKET -> {
                hand.any { it.rank == Rank.BIG_JOKER } &&
                hand.any { it.rank == Rank.SMALL_JOKER }
            }
            else -> false
        }
    }

    /**
     * 获取手牌中的炸弹数量
     */
    fun getBombCount(): Int {
        val rankGroups = hand.groupBy { it.rank }
        var bombCount = rankGroups.count { it.value.size == 4 }

        // 王炸
        if (hand.any { it.rank == Rank.BIG_JOKER } &&
            hand.any { it.rank == Rank.SMALL_JOKER }) {
            bombCount++
        }

        return bombCount
    }
    
    /**
     * 获取最后出的牌
     */
    fun getLastPlayedCards(): List<Card>? {
        return playedCards.lastOrNull()
    }
    
    /**
     * 是否已出完所有牌
     */
    val hasWon: Boolean
        get() = hand.isEmpty()
    
    /**
     * 获取AI难度等级
     */
    val aiDifficulty: Int
        get() = when (type) {
            PlayerType.AI_EASY -> 1
            PlayerType.AI_MEDIUM -> 2
            PlayerType.AI_HARD -> 3
            else -> 0
        }
    
    companion object {
        /**
         * 创建人类玩家
         */
        fun createHuman(name: String = "玩家"): Player {
            return Player(
                id = "human",
                name = name,
                type = PlayerType.HUMAN,
                position = PlayerPosition.BOTTOM
            )
        }
        
        /**
         * 创建AI玩家
         */
        fun createAI(
            id: String,
            name: String,
            difficulty: PlayerType,
            position: PlayerPosition
        ): Player {
            return Player(
                id = id,
                name = name,
                type = difficulty,
                position = position
            )
        }
    }
}
