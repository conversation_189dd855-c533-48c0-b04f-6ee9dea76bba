pluginManagement {
    repositories {
        // 使用阿里云镜像，避免SSL问题
        maven {
            url = uri("https://maven.aliyun.com/repository/gradle-plugin")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/google")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/central")
            isAllowInsecureProtocol = true
        }
        // 备用仓库
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_PROJECT)
    repositories {
        // 使用阿里云镜像
        maven {
            url = uri("https://maven.aliyun.com/repository/google")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/central")
            isAllowInsecureProtocol = true
        }
        // 备用仓库
        google()
        mavenCentral()
    }
}

rootProject.name = "My Application"
include(":app")
 