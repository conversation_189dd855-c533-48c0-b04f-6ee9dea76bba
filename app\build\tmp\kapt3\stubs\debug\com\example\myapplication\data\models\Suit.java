package com.example.myapplication.data.models;

/**
 * 扑克牌花色枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u001f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013\u00a8\u0006\u0014"}, d2 = {"Lcom/example/myapplication/data/models/Suit;", "", "value", "", "symbol", "", "color", "Lcom/example/myapplication/data/models/CardColor;", "(Ljava/lang/String;IILjava/lang/String;Lcom/example/myapplication/data/models/CardColor;)V", "getColor", "()Lcom/example/myapplication/data/models/CardColor;", "getSymbol", "()Ljava/lang/String;", "getValue", "()I", "SPADES", "HEARTS", "CLUBS", "DIAMONDS", "JOKER", "app_debug"})
public enum Suit {
    /*public static final*/ SPADES /* = new SPADES(0, null, null) */,
    /*public static final*/ HEARTS /* = new HEARTS(0, null, null) */,
    /*public static final*/ CLUBS /* = new CLUBS(0, null, null) */,
    /*public static final*/ DIAMONDS /* = new DIAMONDS(0, null, null) */,
    /*public static final*/ JOKER /* = new JOKER(0, null, null) */;
    private final int value = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String symbol = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.models.CardColor color = null;
    
    Suit(int value, java.lang.String symbol, com.example.myapplication.data.models.CardColor color) {
    }
    
    public final int getValue() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSymbol() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.CardColor getColor() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.myapplication.data.models.Suit> getEntries() {
        return null;
    }
}