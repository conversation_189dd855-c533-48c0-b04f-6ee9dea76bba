package com.example.myapplication.domain;

/**
 * 玩家管理器
 * 负责管理玩家状态、手牌、出牌记录等
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010$\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0012\u001a\u00020\u0013J&\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00052\u0006\u0010\u0017\u001a\u00020\u00052\u0006\u0010\u0018\u001a\u00020\u00132\u0006\u0010\u0019\u001a\u00020\u001aJ\u0016\u0010\u001b\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u00052\u0006\u0010\u001d\u001a\u00020\u0005J\u001c\u0010\u001e\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u00052\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020 0\bJ\b\u0010!\u001a\u0004\u0018\u00010\tJ\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\t0\bJ\b\u0010#\u001a\u0004\u0018\u00010\tJ\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\t0\bJ!\u0010%\u001a\u0004\u0018\u00010\u00052\u0012\u0010&\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\'\u00a2\u0006\u0002\u0010(J\b\u0010)\u001a\u0004\u0018\u00010\tJ\b\u0010*\u001a\u0004\u0018\u00010\tJ\u0016\u0010+\u001a\n\u0012\u0004\u0012\u00020 \u0018\u00010\b2\u0006\u0010\u001c\u001a\u00020\u0005J\u000e\u0010,\u001a\u00020\u00052\u0006\u0010-\u001a\u00020\u0005J\u0010\u0010.\u001a\u0004\u0018\u00010\t2\u0006\u0010/\u001a\u00020\u0005J\f\u00100\u001a\b\u0012\u0004\u0012\u00020\u00050\bJ\u0015\u00101\u001a\u0004\u0018\u00010\u00052\u0006\u00102\u001a\u000203\u00a2\u0006\u0002\u00104J\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u000206\u0012\u0004\u0012\u00020\u00010\'J\u000e\u00107\u001a\u00020\u00052\u0006\u0010-\u001a\u00020\u0005J$\u00108\u001a\u00020\u00152\b\b\u0002\u00109\u001a\u0002062\b\b\u0002\u0010:\u001a\u00020;2\b\b\u0002\u0010<\u001a\u00020;J\u0006\u0010=\u001a\u00020\u0015J\u000e\u0010>\u001a\u00020\u00152\u0006\u0010\u001c\u001a\u00020\u0005J\u001c\u0010?\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u00052\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020 0\bJ\u0006\u0010@\u001a\u00020\u0015J\u000e\u0010A\u001a\u00020\u00152\u0006\u0010/\u001a\u00020\u0005J\u001c\u0010B\u001a\u00020\u00152\u0006\u0010\u001c\u001a\u00020\u00052\f\u0010C\u001a\b\u0012\u0004\u0012\u00020 0\bR\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u001d\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\r\u00a8\u0006D"}, d2 = {"Lcom/example/myapplication/domain/PlayerManager;", "", "()V", "_currentPlayerIndex", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_landlordIndex", "_players", "", "Lcom/example/myapplication/data/models/Player;", "currentPlayerIndex", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentPlayerIndex", "()Lkotlinx/coroutines/flow/StateFlow;", "landlordIndex", "getLandlordIndex", "players", "getPlayers", "allPlayersCalledLandlord", "", "calculateScores", "", "baseScore", "bombCount", "isSpring", "winnerRole", "Lcom/example/myapplication/data/models/PlayerRole;", "callLandlord", "playerIndex", "score", "canPlayCards", "cards", "Lcom/example/myapplication/data/models/Card;", "checkWinner", "getAIPlayers", "getCurrentPlayer", "getFarmerPlayers", "getHighestCallingPlayer", "callingScores", "", "(Ljava/util/Map;)Ljava/lang/Integer;", "getHumanPlayer", "getLandlordPlayer", "getLastPlayedCards", "getNextPlayerIndex", "currentIndex", "getPlayer", "index", "getPlayerHandSizes", "getPlayerIndexByPosition", "position", "Lcom/example/myapplication/data/models/PlayerPosition;", "(Lcom/example/myapplication/data/models/PlayerPosition;)Ljava/lang/Integer;", "getPlayerStats", "", "getPreviousPlayerIndex", "initializePlayers", "humanPlayerName", "ai1Difficulty", "Lcom/example/myapplication/data/models/PlayerType;", "ai2Difficulty", "nextPlayer", "pass", "playCards", "resetPlayers", "setCurrentPlayer", "setLandlord", "landlordCards", "app_debug"})
public final class PlayerManager {
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.myapplication.data.models.Player>> _players = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.myapplication.data.models.Player>> players = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Integer> _currentPlayerIndex = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> currentPlayerIndex = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Integer> _landlordIndex = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> landlordIndex = null;
    
    public PlayerManager() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.myapplication.data.models.Player>> getPlayers() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> getCurrentPlayerIndex() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> getLandlordIndex() {
        return null;
    }
    
    /**
     * 初始化玩家
     */
    public final void initializePlayers(@org.jetbrains.annotations.NotNull()
    java.lang.String humanPlayerName, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType ai1Difficulty, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType ai2Difficulty) {
    }
    
    /**
     * 获取指定索引的玩家
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.Player getPlayer(int index) {
        return null;
    }
    
    /**
     * 获取当前玩家
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.Player getCurrentPlayer() {
        return null;
    }
    
    /**
     * 获取地主玩家
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.Player getLandlordPlayer() {
        return null;
    }
    
    /**
     * 获取农民玩家列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.models.Player> getFarmerPlayers() {
        return null;
    }
    
    /**
     * 获取人类玩家
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.Player getHumanPlayer() {
        return null;
    }
    
    /**
     * 获取AI玩家列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.models.Player> getAIPlayers() {
        return null;
    }
    
    /**
     * 切换到下一个玩家
     */
    public final void nextPlayer() {
    }
    
    /**
     * 设置当前玩家
     */
    public final void setCurrentPlayer(int index) {
    }
    
    /**
     * 设置地主
     */
    public final void setLandlord(int playerIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> landlordCards) {
    }
    
    /**
     * 玩家叫地主
     */
    public final boolean callLandlord(int playerIndex, int score) {
        return false;
    }
    
    /**
     * 玩家出牌
     */
    public final boolean playCards(int playerIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards) {
        return false;
    }
    
    /**
     * 玩家过牌
     */
    public final void pass(int playerIndex) {
    }
    
    /**
     * 检查是否有玩家获胜
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.Player checkWinner() {
        return null;
    }
    
    /**
     * 获取玩家手牌数量
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Integer> getPlayerHandSizes() {
        return null;
    }
    
    /**
     * 获取玩家最后出的牌
     */
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.myapplication.data.models.Card> getLastPlayedCards(int playerIndex) {
        return null;
    }
    
    /**
     * 重置所有玩家状态
     */
    public final void resetPlayers() {
    }
    
    /**
     * 计算玩家得分
     */
    public final void calculateScores(int baseScore, int bombCount, boolean isSpring, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerRole winnerRole) {
    }
    
    /**
     * 获取玩家统计信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getPlayerStats() {
        return null;
    }
    
    /**
     * 验证玩家是否可以出指定的牌
     */
    public final boolean canPlayCards(int playerIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards) {
        return false;
    }
    
    /**
     * 获取玩家在指定位置的相对索引
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getPlayerIndexByPosition(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerPosition position) {
        return null;
    }
    
    /**
     * 获取下一个玩家索引
     */
    public final int getNextPlayerIndex(int currentIndex) {
        return 0;
    }
    
    /**
     * 获取上一个玩家索引
     */
    public final int getPreviousPlayerIndex(int currentIndex) {
        return 0;
    }
    
    /**
     * 检查是否所有玩家都已叫过地主
     */
    public final boolean allPlayersCalledLandlord() {
        return false;
    }
    
    /**
     * 获取叫地主分数最高的玩家
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getHighestCallingPlayer(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, java.lang.Integer> callingScores) {
        return null;
    }
}