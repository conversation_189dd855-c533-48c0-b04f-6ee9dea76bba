package com.example.myapplication.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import com.example.myapplication.data.repository.Theme

/**
 * 主题管理器
 */
object ThemeManager {
    
    /**
     * 获取当前主题的颜色方案
     */
    @Composable
    fun getColorScheme(theme: Theme): ColorScheme {
        val isDarkTheme = when (theme) {
            Theme.LIGHT -> false
            Theme.DARK -> true
            Theme.AUTO -> isSystemInDarkTheme()
        }
        
        return if (isDarkTheme) {
            getDarkColorScheme()
        } else {
            getLightColorScheme()
        }
    }
    
    /**
     * 浅色主题颜色方案
     */
    private fun getLightColorScheme(): ColorScheme {
        return lightColorScheme(
            primary = Color(0xFF2E7D32),           // 深绿色
            onPrimary = Color.White,
            primaryContainer = Color(0xFFA5D6A7),  // 浅绿色
            onPrimaryContainer = Color(0xFF1B5E20),
            
            secondary = Color(0xFF8D6E63),          // 棕色
            onSecondary = Color.White,
            secondaryContainer = Color(0xFFD7CCC8), // 浅棕色
            onSecondaryContainer = Color(0xFF5D4037),
            
            tertiary = Color(0xFF1976D2),           // 蓝色
            onTertiary = Color.White,
            tertiaryContainer = Color(0xFFBBDEFB),  // 浅蓝色
            onTertiaryContainer = Color(0xFF0D47A1),
            
            error = Color(0xFFD32F2F),              // 红色
            onError = Color.White,
            errorContainer = Color(0xFFFFCDD2),     // 浅红色
            onErrorContainer = Color(0xFFB71C1C),
            
            background = Color(0xFFF1F8E9),         // 浅绿背景
            onBackground = Color(0xFF1B5E20),
            surface = Color.White,
            onSurface = Color(0xFF1B5E20),
            surfaceVariant = Color(0xFFE8F5E8),     // 浅绿变体
            onSurfaceVariant = Color(0xFF2E7D32),
            
            outline = Color(0xFF81C784),            // 绿色边框
            outlineVariant = Color(0xFFC8E6C9),     // 浅绿边框
            
            scrim = Color.Black.copy(alpha = 0.5f)
        )
    }
    
    /**
     * 深色主题颜色方案
     */
    private fun getDarkColorScheme(): ColorScheme {
        return darkColorScheme(
            primary = Color(0xFF4CAF50),            // 亮绿色
            onPrimary = Color(0xFF1B5E20),
            primaryContainer = Color(0xFF2E7D32),   // 深绿色
            onPrimaryContainer = Color(0xFFA5D6A7),
            
            secondary = Color(0xFFBCAAA4),          // 浅棕色
            onSecondary = Color(0xFF3E2723),
            secondaryContainer = Color(0xFF5D4037),  // 深棕色
            onSecondaryContainer = Color(0xFFD7CCC8),
            
            tertiary = Color(0xFF64B5F6),           // 亮蓝色
            onTertiary = Color(0xFF0D47A1),
            tertiaryContainer = Color(0xFF1976D2),   // 深蓝色
            onTertiaryContainer = Color(0xFFBBDEFB),
            
            error = Color(0xFFEF5350),              // 亮红色
            onError = Color(0xFFB71C1C),
            errorContainer = Color(0xFFD32F2F),     // 深红色
            onErrorContainer = Color(0xFFFFCDD2),
            
            background = Color(0xFF0D1B0F),         // 深绿背景
            onBackground = Color(0xFFA5D6A7),
            surface = Color(0xFF1B2E1D),            // 深绿表面
            onSurface = Color(0xFFA5D6A7),
            surfaceVariant = Color(0xFF2E4A30),     // 深绿变体
            onSurfaceVariant = Color(0xFF81C784),
            
            outline = Color(0xFF4CAF50),            // 亮绿边框
            outlineVariant = Color(0xFF2E7D32),     // 深绿边框
            
            scrim = Color.Black.copy(alpha = 0.7f)
        )
    }
    
    /**
     * 游戏专用颜色
     */
    object GameColors {
        // 卡牌颜色
        val cardBackground = Color.White
        val cardBorder = Color(0xFFBDBDBD)
        val cardSelected = Color(0xFF2196F3)
        
        // 花色颜色
        val redSuit = Color(0xFFD32F2F)      // 红桃、方块
        val blackSuit = Color(0xFF212121)    // 黑桃、梅花
        val jokerColor = Color(0xFF9C27B0)   // 王牌
        
        // 牌桌颜色
        val tableGreen = Color(0xFF2E7D32)   // 牌桌绿色
        val tableDark = Color(0xFF1B5E20)    // 深色牌桌
        
        // 玩家角色颜色
        val landlordColor = Color(0xFFFF9800)  // 地主橙色
        val farmerColor = Color(0xFF4CAF50)    // 农民绿色
        
        // 状态颜色
        val winColor = Color(0xFF4CAF50)       // 胜利绿色
        val loseColor = Color(0xFFD32F2F)      // 失败红色
        val warningColor = Color(0xFFFF9800)   // 警告橙色
        
        // 特效颜色
        val bombColor = Color(0xFFFF5722)      // 炸弹红色
        val rocketColor = Color(0xFF9C27B0)    // 王炸紫色
    }
    
    /**
     * 游戏专用尺寸
     */
    object GameDimensions {
        // 卡牌尺寸
        val cardWidthSmall = 32.dp
        val cardHeightSmall = 44.dp
        val cardWidthNormal = 48.dp
        val cardHeightNormal = 68.dp
        val cardWidthLarge = 64.dp
        val cardHeightLarge = 88.dp
        
        // 间距
        val cardSpacing = 4.dp
        val cardOverlap = (-16).dp
        val sectionSpacing = 16.dp
        val screenPadding = 16.dp
        
        // 圆角
        val cardCornerRadius = 6.dp
        val buttonCornerRadius = 12.dp
        val dialogCornerRadius = 16.dp
        
        // 阴影
        val cardElevation = 4.dp
        val buttonElevation = 2.dp
        val dialogElevation = 8.dp
    }
    
    /**
     * 游戏专用字体大小
     */
    object GameTypography {
        val cardRankSize = 14.sp
        val cardSuitSize = 16.sp
        val playerNameSize = 14.sp
        val scoreSize = 16.sp
        val titleSize = 24.sp
        val buttonTextSize = 16.sp
        val hintTextSize = 12.sp
    }
    
    /**
     * 动画持续时间
     */
    object AnimationDurations {
        const val cardFlip = 600
        const val cardPlay = 300
        const val bombExplosion = 500
        const val fadeInOut = 300
        const val slideInOut = 400
        const val scaleInOut = 250
        const val dealingCard = 200
        const val shuffleCards = 1000
    }
    
    /**
     * 获取卡牌颜色
     */
    fun getCardColor(card: com.example.myapplication.data.models.Card): Color {
        return when {
            card.isJoker -> GameColors.jokerColor
            card.suit.color == com.example.myapplication.data.models.CardColor.RED -> GameColors.redSuit
            else -> GameColors.blackSuit
        }
    }
    
    /**
     * 获取玩家角色颜色
     */
    fun getPlayerRoleColor(role: com.example.myapplication.data.models.PlayerRole?): Color {
        return when (role) {
            com.example.myapplication.data.models.PlayerRole.LANDLORD -> GameColors.landlordColor
            com.example.myapplication.data.models.PlayerRole.FARMER -> GameColors.farmerColor
            null -> Color.Gray
        }
    }
    
    /**
     * 获取牌型颜色
     */
    fun getPatternColor(pattern: com.example.myapplication.data.models.CardPattern): Color {
        return when {
            pattern.type == com.example.myapplication.data.models.CardPatternType.ROCKET -> GameColors.rocketColor
            pattern.type == com.example.myapplication.data.models.CardPatternType.BOMB -> GameColors.bombColor
            else -> Color.Unspecified
        }
    }
}
