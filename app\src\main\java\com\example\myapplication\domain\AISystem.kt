package com.example.myapplication.domain

import com.example.myapplication.data.models.*
import kotlin.random.Random

/**
 * AI系统
 * 负责AI玩家的决策逻辑
 */
class AISystem {
    
    private val cardLogic = CardLogic()
    
    /**
     * AI叫地主决策
     */
    fun makeCallingDecision(
        player: Player,
        hand: List<Card>,
        maxCallingScore: Int,
        landlordCards: List<Card>
    ): Int {
        val handStrength = evaluateHandStrength(hand + landlordCards)
        
        return when (player.type) {
            PlayerType.AI_EASY -> makeEasyCallingDecision(handStrength, maxCallingScore)
            PlayerType.AI_MEDIUM -> makeMediumCallingDecision(handStrength, maxCallingScore)
            PlayerType.AI_HARD -> makeHardCallingDecision(handStrength, maxCallingScore, hand, landlordCards)
            else -> 0
        }
    }
    
    /**
     * AI出牌决策
     */
    fun makePlayDecision(
        player: Player,
        hand: List<Card>,
        lastPlayedPattern: CardPattern?,
        gameState: GameState,
        allPlayers: List<Player>
    ): List<Card>? {
        val possiblePlays = cardLogic.getPossiblePlays(hand, lastPlayedPattern)
        
        if (possiblePlays.isEmpty()) return null
        
        return when (player.type) {
            PlayerType.AI_EASY -> makeEasyPlayDecision(possiblePlays)
            PlayerType.AI_MEDIUM -> makeMediumPlayDecision(possiblePlays, hand, lastPlayedPattern)
            PlayerType.AI_HARD -> makeHardPlayDecision(possiblePlays, hand, gameState, allPlayers)
            else -> null
        }
    }
    
    /**
     * 评估手牌强度
     */
    private fun evaluateHandStrength(hand: List<Card>): Double {
        var strength = 0.0
        val rankCounts = hand.groupingBy { it.rank }.eachCount()

        // 王牌加分
        if (rankCounts.containsKey(Rank.BIG_JOKER)) strength += 15.0
        if (rankCounts.containsKey(Rank.SMALL_JOKER)) strength += 10.0

        // 炸弹和牌型加分
        rankCounts.values.forEach { count ->
            when (count) {
                4 -> strength += 25.0  // 炸弹
                3 -> strength += 10.0  // 三张
                2 -> strength += 4.0   // 对子
            }
        }

        // 大牌加分
        rankCounts.forEach { (rank, count) ->
            when (rank.value) {
                15 -> strength += count * 4.0  // 2
                14 -> strength += count * 3.5  // A
                13 -> strength += count * 2.5  // K
                12 -> strength += count * 2.0  // Q
                11 -> strength += count * 1.5  // J
                10 -> strength += count * 1.2  // 10
            }
        }

        // 顺子潜力加分
        val straightPotential = evaluateStraightPotential(hand)
        strength += straightPotential

        // 连对潜力加分
        val pairStraightPotential = evaluatePairStraightPotential(hand)
        strength += pairStraightPotential

        // 手牌平衡性评估
        val balanceBonus = evaluateHandBalance(hand)
        strength += balanceBonus

        return strength
    }
    
    /**
     * 评估顺子潜力
     */
    private fun evaluateStraightPotential(hand: List<Card>): Double {
        val normalCards = hand.filter { !it.isJoker && it.rank.value < 15 }
        val ranks = normalCards.map { it.rank }.distinct().sorted()

        var potential = 0.0
        var consecutiveCount = 1

        for (i in 1 until ranks.size) {
            if (ranks[i].value == ranks[i-1].value + 1) {
                consecutiveCount++
            } else {
                if (consecutiveCount >= 5) {
                    potential += consecutiveCount * 3.0
                } else if (consecutiveCount >= 3) {
                    potential += consecutiveCount * 1.5
                }
                consecutiveCount = 1
            }
        }

        if (consecutiveCount >= 5) {
            potential += consecutiveCount * 3.0
        } else if (consecutiveCount >= 3) {
            potential += consecutiveCount * 1.5
        }

        return potential
    }

    /**
     * 评估连对潜力
     */
    private fun evaluatePairStraightPotential(hand: List<Card>): Double {
        val rankCounts = hand.groupingBy { it.rank }.eachCount()
        val pairs = rankCounts.filter { it.value >= 2 && it.key.value < 15 }
        val sortedPairRanks = pairs.keys.sorted()

        var potential = 0.0
        var consecutiveCount = 1

        for (i in 1 until sortedPairRanks.size) {
            if (sortedPairRanks[i].value == sortedPairRanks[i-1].value + 1) {
                consecutiveCount++
            } else {
                if (consecutiveCount >= 3) {
                    potential += consecutiveCount * 2.5
                }
                consecutiveCount = 1
            }
        }

        if (consecutiveCount >= 3) {
            potential += consecutiveCount * 2.5
        }

        return potential
    }

    /**
     * 评估手牌平衡性
     */
    private fun evaluateHandBalance(hand: List<Card>): Double {
        val rankCounts = hand.groupingBy { it.rank }.eachCount()
        var balance = 0.0

        // 单张牌过多扣分
        val singleCount = rankCounts.count { it.value == 1 }
        if (singleCount > hand.size * 0.6) {
            balance -= singleCount * 0.5
        }

        // 有配对的牌加分
        val pairedCount = rankCounts.count { it.value >= 2 }
        balance += pairedCount * 1.0

        // 牌型多样性加分
        val hasTriples = rankCounts.any { it.value >= 3 }
        val hasPairs = rankCounts.any { it.value == 2 }
        val hasBombs = rankCounts.any { it.value == 4 }

        if (hasTriples) balance += 2.0
        if (hasPairs) balance += 1.0
        if (hasBombs) balance += 5.0

        return balance
    }
    
    /**
     * 简单AI叫地主决策
     */
    private fun makeEasyCallingDecision(handStrength: Double, maxCallingScore: Int): Int {
        return when {
            handStrength > 60.0 && maxCallingScore < 3 -> 3
            handStrength > 45.0 && maxCallingScore < 2 -> 2
            handStrength > 30.0 && maxCallingScore < 1 -> 1
            else -> 0
        }
    }
    
    /**
     * 中等AI叫地主决策
     */
    private fun makeMediumCallingDecision(handStrength: Double, maxCallingScore: Int): Int {
        val threshold = when (maxCallingScore) {
            0 -> 25.0
            1 -> 40.0
            2 -> 55.0
            else -> 70.0
        }
        
        return if (handStrength > threshold) {
            maxCallingScore + 1
        } else {
            0
        }
    }
    
    /**
     * 困难AI叫地主决策
     */
    private fun makeHardCallingDecision(
        handStrength: Double,
        maxCallingScore: Int,
        hand: List<Card>,
        landlordCards: List<Card>
    ): Int {
        // 考虑底牌的价值
        val landlordValue = evaluateLandlordCards(hand, landlordCards)
        val totalStrength = handStrength + landlordValue
        
        // 考虑位置因素（后叫地主有优势）
        val positionBonus = 5.0
        val adjustedStrength = totalStrength + positionBonus
        
        val threshold = when (maxCallingScore) {
            0 -> 30.0
            1 -> 45.0
            2 -> 60.0
            else -> 75.0
        }
        
        return if (adjustedStrength > threshold) {
            minOf(maxCallingScore + 1, 3)
        } else {
            0
        }
    }
    
    /**
     * 评估底牌价值
     */
    private fun evaluateLandlordCards(hand: List<Card>, landlordCards: List<Card>): Double {
        var value = 0.0
        val handRanks = hand.groupingBy { it.rank }.eachCount()
        
        landlordCards.forEach { card ->
            // 王牌价值高
            if (card.isJoker) {
                value += if (card.isBigJoker) 12.0 else 8.0
                return@forEach
            }
            
            // 能组成炸弹
            if (handRanks.getOrDefault(card.rank, 0) == 3) {
                value += 15.0
                return@forEach
            }
            
            // 能组成三张
            if (handRanks.getOrDefault(card.rank, 0) == 2) {
                value += 6.0
                return@forEach
            }
            
            // 能组成对子
            if (handRanks.getOrDefault(card.rank, 0) == 1) {
                value += 2.0
                return@forEach
            }
            
            // 大牌价值
            when (card.rank.value) {
                15 -> value += 4.0  // 2
                14 -> value += 3.0  // A
                13 -> value += 2.0  // K
                else -> value += 1.0
            }
        }
        
        return value
    }
    
    /**
     * 简单AI出牌决策
     */
    private fun makeEasyPlayDecision(possiblePlays: List<List<Card>>): List<Card> {
        // 随机选择一个可行的出牌
        return possiblePlays.random()
    }
    
    /**
     * 中等AI出牌决策
     */
    private fun makeMediumPlayDecision(
        possiblePlays: List<List<Card>>,
        hand: List<Card>,
        lastPlayedPattern: CardPattern?
    ): List<Card> {
        // 优先出小牌，保留大牌
        val sortedPlays = possiblePlays.sortedBy { cards ->
            val pattern = cardLogic.identifyPattern(cards)
            when {
                pattern.isBomb -> 1000  // 炸弹优先级最低
                pattern.type == CardPatternType.SINGLE -> cards[0].weight
                else -> pattern.weight
            }
        }
        
        // 如果手牌很少，优先出大牌
        return if (hand.size <= 5) {
            sortedPlays.lastOrNull() ?: possiblePlays.random()
        } else {
            sortedPlays.firstOrNull() ?: possiblePlays.random()
        }
    }
    
    /**
     * 困难AI出牌决策
     */
    private fun makeHardPlayDecision(
        possiblePlays: List<List<Card>>,
        hand: List<Card>,
        gameState: GameState,
        allPlayers: List<Player>
    ): List<Card> {
        if (possiblePlays.isEmpty()) return emptyList()

        // 分析游戏局势
        val currentPlayer = allPlayers.find { it.isAI }
        val isLandlord = currentPlayer?.role == PlayerRole.LANDLORD
        val remainingCards = allPlayers.sumOf { it.handSize }
        val isEndGame = remainingCards <= 15
        val isLateGame = remainingCards <= 25

        // 分析对手情况
        val opponents = allPlayers.filter { it.id != currentPlayer?.id }
        val minOpponentCards = opponents.minOfOrNull { it.handSize } ?: 17
        val maxOpponentCards = opponents.maxOfOrNull { it.handSize } ?: 17

        // 根据局势选择策略
        return when {
            isEndGame -> chooseEndGameStrategy(possiblePlays, hand, minOpponentCards)
            isLateGame -> chooseLateGameStrategy(possiblePlays, hand, isLandlord, minOpponentCards)
            isLandlord -> chooseLandlordStrategy(possiblePlays, hand, allPlayers)
            else -> chooseFarmerStrategy(possiblePlays, hand, allPlayers, gameState)
        }
    }
    
    /**
     * 残局策略
     */
    private fun chooseEndGameStrategy(
        possiblePlays: List<List<Card>>,
        hand: List<Card>,
        minOpponentCards: Int
    ): List<Card> {
        // 如果对手手牌很少，优先出大牌压制
        if (minOpponentCards <= 3) {
            return possiblePlays.maxByOrNull { cards ->
                val pattern = cardLogic.identifyPattern(cards)
                pattern.weight
            } ?: possiblePlays.random()
        }

        // 否则优先出能清理手牌的组合
        val bestPlay = possiblePlays.maxByOrNull { cards ->
            val pattern = cardLogic.identifyPattern(cards)
            val remainingAfterPlay = hand.size - cards.size

            // 评分：优先考虑能大幅减少手牌的出牌
            var score = cards.size * 10.0

            // 如果出完后手牌很少，额外加分
            if (remainingAfterPlay <= 2) score += 50.0
            else if (remainingAfterPlay <= 5) score += 20.0

            // 炸弹和王炸在残局很有价值
            if (pattern.isBomb) score += 30.0

            score
        }

        return bestPlay ?: possiblePlays.random()
    }

    /**
     * 后期策略
     */
    private fun chooseLateGameStrategy(
        possiblePlays: List<List<Card>>,
        hand: List<Card>,
        isLandlord: Boolean,
        minOpponentCards: Int
    ): List<Card> {
        // 后期更注重控制节奏
        return if (isLandlord) {
            // 地主在后期要控制局面
            if (minOpponentCards <= 5) {
                // 对手手牌少，出大牌压制
                possiblePlays.maxByOrNull { cards ->
                    cardLogic.identifyPattern(cards).weight
                } ?: possiblePlays.random()
            } else {
                // 出中等牌，保留实力
                possiblePlays.sortedBy { cards ->
                    cardLogic.identifyPattern(cards).weight
                }.getOrNull(possiblePlays.size / 2) ?: possiblePlays.random()
            }
        } else {
            // 农民要配合，不轻易出大牌
            possiblePlays.minByOrNull { cards ->
                val pattern = cardLogic.identifyPattern(cards)
                if (pattern.isBomb) 1000 else pattern.weight
            } ?: possiblePlays.random()
        }
    }
    
    /**
     * 地主策略
     */
    private fun chooseLandlordStrategy(
        possiblePlays: List<List<Card>>,
        hand: List<Card>,
        allPlayers: List<Player>
    ): List<Card> {
        val farmers = allPlayers.filter { it.role == PlayerRole.FARMER }
        val minFarmerCards = farmers.minOfOrNull { it.handSize } ?: 17
        
        // 如果农民手牌很少，优先出大牌压制
        return if (minFarmerCards <= 3) {
            possiblePlays.maxByOrNull { cards ->
                val pattern = cardLogic.identifyPattern(cards)
                pattern.weight
            } ?: possiblePlays.random()
        } else {
            // 否则出小牌，保留实力
            possiblePlays.minByOrNull { cards ->
                val pattern = cardLogic.identifyPattern(cards)
                if (pattern.isBomb) 1000 else pattern.weight
            } ?: possiblePlays.random()
        }
    }
    
    /**
     * 农民策略
     */
    private fun chooseFarmerStrategy(
        possiblePlays: List<List<Card>>,
        hand: List<Card>,
        allPlayers: List<Player>,
        gameState: GameState
    ): List<Card> {
        val landlord = allPlayers.find { it.role == PlayerRole.LANDLORD }
        val landlordCards = landlord?.handSize ?: 20
        
        // 如果地主手牌很少，优先阻止
        return if (landlordCards <= 3) {
            possiblePlays.maxByOrNull { cards ->
                val pattern = cardLogic.identifyPattern(cards)
                pattern.weight
            } ?: possiblePlays.random()
        } else {
            // 否则配合队友，出适中的牌
            possiblePlays.sortedBy { cards ->
                val pattern = cardLogic.identifyPattern(cards)
                pattern.weight
            }.getOrNull(possiblePlays.size / 2) ?: possiblePlays.random()
        }
    }
}
