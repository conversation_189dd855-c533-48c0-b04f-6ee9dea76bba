package com.example.myapplication.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u0010\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u001a$\u0010\u0007\u001a\u00020\u00012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u00a8\u0006\u000b"}, d2 = {"LoadingScreen", "", "message", "", "MainScreen", "gameViewModel", "Lcom/example/myapplication/ui/viewmodel/GameViewModel;", "WelcomeScreen", "onStartGame", "Lkotlin/Function0;", "onShowSettings", "app_debug"})
public final class MainScreenKt {
    
    /**
     * 主界面
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MainScreen(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.viewmodel.GameViewModel gameViewModel) {
    }
    
    /**
     * 欢迎界面
     */
    @androidx.compose.runtime.Composable()
    public static final void WelcomeScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartGame, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onShowSettings) {
    }
    
    /**
     * 加载界面
     */
    @androidx.compose.runtime.Composable()
    public static final void LoadingScreen(@org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
}