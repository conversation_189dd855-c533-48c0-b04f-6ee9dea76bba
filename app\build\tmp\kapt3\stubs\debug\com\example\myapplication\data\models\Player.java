package com.example.myapplication.data.models;

/**
 * 玩家数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b*\n\u0002\u0010\u0002\n\u0002\b\u0013\n\u0002\u0010\u0000\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0087\b\u0018\u0000 ^2\u00020\u0001:\u0001^B\u007f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u0012\u0014\b\u0002\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u000f0\f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0013\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0013\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0011\u00a2\u0006\u0002\u0010\u0016J\u000e\u0010=\u001a\u00020>2\u0006\u0010?\u001a\u00020\rJ\u0014\u0010@\u001a\u00020>2\f\u0010A\u001a\b\u0012\u0004\u0012\u00020\r0\u000fJ\u0006\u0010B\u001a\u00020>J\t\u0010C\u001a\u00020\u0003H\u00c6\u0003J\t\u0010D\u001a\u00020\u0013H\u00c6\u0003J\t\u0010E\u001a\u00020\u0011H\u00c6\u0003J\t\u0010F\u001a\u00020\u0003H\u00c6\u0003J\t\u0010G\u001a\u00020\u0006H\u00c6\u0003J\t\u0010H\u001a\u00020\bH\u00c6\u0003J\u000b\u0010I\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000f\u0010J\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u00c6\u0003J\u0015\u0010K\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u000f0\fH\u00c6\u0003J\t\u0010L\u001a\u00020\u0011H\u00c6\u0003J\t\u0010M\u001a\u00020\u0013H\u00c6\u0003J\u008b\u0001\u0010N\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0014\b\u0002\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u000f0\f2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u00132\b\b\u0002\u0010\u0015\u001a\u00020\u0011H\u00c6\u0001J\t\u0010O\u001a\u00020\u0011H\u00d6\u0001J\u0013\u0010P\u001a\u00020\u00132\b\u0010Q\u001a\u0004\u0018\u00010RH\u00d6\u0003J\u000e\u0010S\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\u000fJ\t\u0010T\u001a\u00020\u0011H\u00d6\u0001J\u0014\u0010U\u001a\u00020\u00132\f\u0010A\u001a\b\u0012\u0004\u0012\u00020\r0\u000fJ\u0014\u0010V\u001a\u00020\u00132\f\u0010A\u001a\b\u0012\u0004\u0012\u00020\r0\u000fJ\u0006\u0010W\u001a\u00020>J\u0006\u0010X\u001a\u00020>J\t\u0010Y\u001a\u00020\u0003H\u00d6\u0001J\u0019\u0010Z\u001a\u00020>2\u0006\u0010[\u001a\u00020\\2\u0006\u0010]\u001a\u00020\u0011H\u00d6\u0001R\u0011\u0010\u0017\u001a\u00020\u00118F\u00a2\u0006\u0006\u001a\u0004\b\u0018\u0010\u0019R \u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u001b\"\u0004\b\u001c\u0010\u001dR\u0011\u0010\u001e\u001a\u00020\u00118F\u00a2\u0006\u0006\u001a\u0004\b\u001f\u0010\u0019R\u001a\u0010\u0014\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b \u0010!\"\u0004\b\"\u0010#R\u0011\u0010$\u001a\u00020\u00138F\u00a2\u0006\u0006\u001a\u0004\b%\u0010!R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010(\u001a\u00020\u00138F\u00a2\u0006\u0006\u001a\u0004\b(\u0010!R\u001a\u0010\u0012\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010!\"\u0004\b)\u0010#R\u0011\u0010*\u001a\u00020\u00138F\u00a2\u0006\u0006\u001a\u0004\b*\u0010!R\u0011\u0010+\u001a\u00020\u00138F\u00a2\u0006\u0006\u001a\u0004\b+\u0010!R\u0011\u0010,\u001a\u00020\u00138F\u00a2\u0006\u0006\u001a\u0004\b,\u0010!R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\'R\u001a\u0010\u0015\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b.\u0010\u0019\"\u0004\b/\u00100R&\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u000f0\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b1\u0010\u001b\"\u0004\b2\u0010\u001dR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u00104R\u001c\u0010\t\u001a\u0004\u0018\u00010\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u00106\"\u0004\b7\u00108R\u001a\u0010\u0010\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b9\u0010\u0019\"\u0004\b:\u00100R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010<\u00a8\u0006_"}, d2 = {"Lcom/example/myapplication/data/models/Player;", "Landroid/os/Parcelable;", "id", "", "name", "type", "Lcom/example/myapplication/data/models/PlayerType;", "position", "Lcom/example/myapplication/data/models/PlayerPosition;", "role", "Lcom/example/myapplication/data/models/PlayerRole;", "hand", "", "Lcom/example/myapplication/data/models/Card;", "playedCards", "", "score", "", "isActive", "", "hasCalledLandlord", "passCount", "(Ljava/lang/String;Ljava/lang/String;Lcom/example/myapplication/data/models/PlayerType;Lcom/example/myapplication/data/models/PlayerPosition;Lcom/example/myapplication/data/models/PlayerRole;Ljava/util/List;Ljava/util/List;IZZI)V", "aiDifficulty", "getAiDifficulty", "()I", "getHand", "()Ljava/util/List;", "setHand", "(Ljava/util/List;)V", "handSize", "getHandSize", "getHasCalledLandlord", "()Z", "setHasCalledLandlord", "(Z)V", "hasWon", "getHasWon", "getId", "()Ljava/lang/String;", "isAI", "setActive", "isFarmer", "isHuman", "isLandlord", "getName", "getPassCount", "setPassCount", "(I)V", "getPlayedCards", "setPlayedCards", "getPosition", "()Lcom/example/myapplication/data/models/PlayerPosition;", "getRole", "()Lcom/example/myapplication/data/models/PlayerRole;", "setRole", "(Lcom/example/myapplication/data/models/PlayerRole;)V", "getScore", "setScore", "getType", "()Lcom/example/myapplication/data/models/PlayerType;", "addCard", "", "card", "addCards", "cards", "clearHand", "component1", "component10", "component11", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "describeContents", "equals", "other", "", "getLastPlayedCards", "hashCode", "playCards", "removeCards", "reset", "sortHand", "toString", "writeToParcel", "parcel", "Landroid/os/Parcel;", "flags", "Companion", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class Player implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String name = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.models.PlayerType type = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.models.PlayerPosition position = null;
    @org.jetbrains.annotations.Nullable()
    private com.example.myapplication.data.models.PlayerRole role;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.example.myapplication.data.models.Card> hand;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.util.List<com.example.myapplication.data.models.Card>> playedCards;
    private int score;
    private boolean isActive;
    private boolean hasCalledLandlord;
    private int passCount;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.myapplication.data.models.Player.Companion Companion = null;
    
    public Player(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType type, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerPosition position, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.models.PlayerRole role, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> hand, @org.jetbrains.annotations.NotNull()
    java.util.List<java.util.List<com.example.myapplication.data.models.Card>> playedCards, int score, boolean isActive, boolean hasCalledLandlord, int passCount) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.PlayerType getType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.PlayerPosition getPosition() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.PlayerRole getRole() {
        return null;
    }
    
    public final void setRole(@org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.models.PlayerRole p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.models.Card> getHand() {
        return null;
    }
    
    public final void setHand(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.util.List<com.example.myapplication.data.models.Card>> getPlayedCards() {
        return null;
    }
    
    public final void setPlayedCards(@org.jetbrains.annotations.NotNull()
    java.util.List<java.util.List<com.example.myapplication.data.models.Card>> p0) {
    }
    
    public final int getScore() {
        return 0;
    }
    
    public final void setScore(int p0) {
    }
    
    public final boolean isActive() {
        return false;
    }
    
    public final void setActive(boolean p0) {
    }
    
    public final boolean getHasCalledLandlord() {
        return false;
    }
    
    public final void setHasCalledLandlord(boolean p0) {
    }
    
    public final int getPassCount() {
        return 0;
    }
    
    public final void setPassCount(int p0) {
    }
    
    public final int getHandSize() {
        return 0;
    }
    
    public final boolean isLandlord() {
        return false;
    }
    
    public final boolean isFarmer() {
        return false;
    }
    
    public final boolean isHuman() {
        return false;
    }
    
    public final boolean isAI() {
        return false;
    }
    
    /**
     * 添加牌到手牌
     */
    public final void addCards(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards) {
    }
    
    /**
     * 添加单张牌到手牌
     */
    public final void addCard(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.Card card) {
    }
    
    /**
     * 从手牌中移除指定牌
     */
    public final boolean removeCards(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards) {
        return false;
    }
    
    /**
     * 出牌
     */
    public final boolean playCards(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards) {
        return false;
    }
    
    /**
     * 整理手牌（按权重排序）
     */
    public final void sortHand() {
    }
    
    /**
     * 清空手牌
     */
    public final void clearHand() {
    }
    
    /**
     * 重置玩家状态
     */
    public final void reset() {
    }
    
    /**
     * 获取最后出的牌
     */
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.myapplication.data.models.Card> getLastPlayedCards() {
        return null;
    }
    
    public final boolean getHasWon() {
        return false;
    }
    
    public final int getAiDifficulty() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final int component11() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.PlayerType component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.PlayerPosition component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.PlayerRole component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.models.Card> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.util.List<com.example.myapplication.data.models.Card>> component7() {
        return null;
    }
    
    public final int component8() {
        return 0;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.Player copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType type, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerPosition position, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.models.PlayerRole role, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> hand, @org.jetbrains.annotations.NotNull()
    java.util.List<java.util.List<com.example.myapplication.data.models.Card>> playedCards, int score, boolean isActive, boolean hasCalledLandlord, int passCount) {
        return null;
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J&\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bJ\u0010\u0010\f\u001a\u00020\u00042\b\b\u0002\u0010\u0007\u001a\u00020\u0006\u00a8\u0006\r"}, d2 = {"Lcom/example/myapplication/data/models/Player$Companion;", "", "()V", "createAI", "Lcom/example/myapplication/data/models/Player;", "id", "", "name", "difficulty", "Lcom/example/myapplication/data/models/PlayerType;", "position", "Lcom/example/myapplication/data/models/PlayerPosition;", "createHuman", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 创建人类玩家
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.Player createHuman(@org.jetbrains.annotations.NotNull()
        java.lang.String name) {
            return null;
        }
        
        /**
         * 创建AI玩家
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.Player createAI(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.lang.String name, @org.jetbrains.annotations.NotNull()
        com.example.myapplication.data.models.PlayerType difficulty, @org.jetbrains.annotations.NotNull()
        com.example.myapplication.data.models.PlayerPosition position) {
            return null;
        }
    }
}