com.example.myapplication.app-ui-tooling-preview-release-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0094f6afb3b39c198681fc28d9cde818\transformed\ui-tooling-preview-release\res
com.example.myapplication.app-navigation-common-ktx-2.7.5-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01b03faec934bad0b531dfe4127932ce\transformed\navigation-common-ktx-2.7.5\res
com.example.myapplication.app-lifecycle-livedata-ktx-2.9.1-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0804a85085d7d1c86b2bfc8240d02d61\transformed\lifecycle-livedata-ktx-2.9.1\res
com.example.myapplication.app-ui-text-release-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\124f2f0faf1d1608d0a685e59d00793c\transformed\ui-text-release\res
com.example.myapplication.app-animation-release-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14fbe5ad3e45e87141aa1349fb9e3827\transformed\animation-release\res
com.example.myapplication.app-material-icons-core-release-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16c1161af96986b63ea5353ea7133d37\transformed\material-icons-core-release\res
com.example.myapplication.app-ui-graphics-release-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba551d6c70048f5177e14832f961c8b\transformed\ui-graphics-release\res
com.example.myapplication.app-lifecycle-viewmodel-savedstate-release-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28f076282e34888a7f30cc4ce33983c7\transformed\lifecycle-viewmodel-savedstate-release\res
com.example.myapplication.app-lifecycle-runtime-release-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2de674196157711cb5ad65d1ed732eaa\transformed\lifecycle-runtime-release\res
com.example.myapplication.app-ui-unit-release-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e0bc7d67496b1881d89b2b61782a2cc\transformed\ui-unit-release\res
com.example.myapplication.app-core-viewtree-1.0.0-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f2db81535833d6680e5b05d3551a766\transformed\core-viewtree-1.0.0\res
com.example.myapplication.app-ui-test-manifest-1.7.8-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3421b7ffb9a2be5e4c411e193c9bf977\transformed\ui-test-manifest-1.7.8\res
com.example.myapplication.app-annotation-experimental-1.4.1-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bb6a361aed974a27eced6880c0acd6\transformed\annotation-experimental-1.4.1\res
com.example.myapplication.app-room-runtime-2.6.1-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3af42cdd80fe84658fd8ac8079ca66ad\transformed\room-runtime-2.6.1\res
com.example.myapplication.app-material3-release-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdae484d8629b91b71d2a8e711d605e\transformed\material3-release\res
com.example.myapplication.app-activity-ktx-1.10.1-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fc2d3b173c418954d09a7bae00767a8\transformed\activity-ktx-1.10.1\res
com.example.myapplication.app-profileinstaller-1.4.0-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\res
com.example.myapplication.app-navigation-common-2.7.5-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47ef16df4bb5bf327dd69a12d73ed8b7\transformed\navigation-common-2.7.5\res
com.example.myapplication.app-core-runtime-2.2.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ae577d56c7a6b6cbbd333be755128cb\transformed\core-runtime-2.2.0\res
com.example.myapplication.app-customview-poolingcontainer-1.0.0-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5ad589dfae2725b355be2db74e092c\transformed\customview-poolingcontainer-1.0.0\res
com.example.myapplication.app-lifecycle-viewmodel-ktx-2.9.1-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd589db3c571ad4744a4d3aa2997a67\transformed\lifecycle-viewmodel-ktx-2.9.1\res
com.example.myapplication.app-runtime-release-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c948291b66fa7ebe99389011de6dcaf\transformed\runtime-release\res
com.example.myapplication.app-sqlite-framework-2.4.0-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52a23c5064d773ac87533b2a930fe78b\transformed\sqlite-framework-2.4.0\res
com.example.myapplication.app-foundation-layout-release-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53f5d9bb835026ee287b472503c70501\transformed\foundation-layout-release\res
com.example.myapplication.app-ui-geometry-release-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a2c466ef1e99fd68dc0fbaeff53fa5e\transformed\ui-geometry-release\res
com.example.myapplication.app-startup-runtime-1.1.1-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\res
com.example.myapplication.app-ui-util-release-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6999364550cf340494f92f6d10baed49\transformed\ui-util-release\res
com.example.myapplication.app-core-ktx-1.16.0-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ada138c221f6e89126d76edeeac1901\transformed\core-ktx-1.16.0\res
com.example.myapplication.app-core-1.16.0-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6dce069ed5f76821f2d9938d1900d2a1\transformed\core-1.16.0\res
com.example.myapplication.app-emoji2-1.3.0-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\res
com.example.myapplication.app-lifecycle-runtime-compose-release-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c9efd4c819d735dd95f99810a58d0ad\transformed\lifecycle-runtime-compose-release\res
com.example.myapplication.app-room-ktx-2.6.1-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d5f25c026a50ede6d1a97680d715c1\transformed\room-ktx-2.6.1\res
com.example.myapplication.app-savedstate-release-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87bf3b6ed36062e83c10ffe48a91bc57\transformed\savedstate-release\res
com.example.myapplication.app-tracing-1.2.0-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bce9199b069f5c9c90b4c2e518facb\transformed\tracing-1.2.0\res
com.example.myapplication.app-foundation-release-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\965fb6c3bf340bea42b1b1c81d253a97\transformed\foundation-release\res
com.example.myapplication.app-runtime-saveable-release-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c853c0c249e568c9fc211a92a0d1239\transformed\runtime-saveable-release\res
com.example.myapplication.app-animation-core-release-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a704932a9addc68da362d6198f333365\transformed\animation-core-release\res
com.example.myapplication.app-datastore-preferences-1.0.0-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acdfd22a333cd79faf700d029ed37500\transformed\datastore-preferences-1.0.0\res
com.example.myapplication.app-navigation-compose-2.7.5-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b71c5a4fde29a996c62f7f2eebcacab7\transformed\navigation-compose-2.7.5\res
com.example.myapplication.app-lifecycle-process-2.9.1-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1172e6a16a69b9db6b6e231d7fa1df\transformed\lifecycle-process-2.9.1\res
com.example.myapplication.app-lifecycle-livedata-core-2.9.1-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4000f50ecbacda4cc1bc1f54ade9cc\transformed\lifecycle-livedata-core-2.9.1\res
com.example.myapplication.app-savedstate-ktx-1.3.0-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c24e3a2786dc9073c3182d4647f22277\transformed\savedstate-ktx-1.3.0\res
com.example.myapplication.app-lifecycle-livedata-2.9.1-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7d345efa1104f2a570bd92a19da6537\transformed\lifecycle-livedata-2.9.1\res
com.example.myapplication.app-lifecycle-viewmodel-release-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7d5ee2d09778517c279ba91c66e1189\transformed\lifecycle-viewmodel-release\res
com.example.myapplication.app-lifecycle-viewmodel-compose-release-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9038e0c2aa8a1c13cbf583dc7a5c800\transformed\lifecycle-viewmodel-compose-release\res
com.example.myapplication.app-ui-tooling-release-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca40a302669dfbdd5b33bd0043f07f24\transformed\ui-tooling-release\res
com.example.myapplication.app-activity-1.10.1-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca8518b89e1044a7f88162fa21c7ddc2\transformed\activity-1.10.1\res
com.example.myapplication.app-graphics-path-1.0.1-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d28d2ff9cc6a6a7cce075dc280707e64\transformed\graphics-path-1.0.1\res
com.example.myapplication.app-material-release-48 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d753ba01cbcf41a62ffa3740d4e0e54f\transformed\material-release\res
com.example.myapplication.app-datastore-1.0.0-49 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da2858945a0ca94ea93ea29ff636143c\transformed\datastore-1.0.0\res
com.example.myapplication.app-material-icons-extended-release-50 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2721a18d1f384e325377e5ce198a2b2\transformed\material-icons-extended-release\res
com.example.myapplication.app-lifecycle-livedata-core-ktx-2.9.1-51 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb4341160f5a5540d37642307a57799d\transformed\lifecycle-livedata-core-ktx-2.9.1\res
com.example.myapplication.app-navigation-runtime-2.7.5-52 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed31eac744d4fa401682afb1e43f594a\transformed\navigation-runtime-2.7.5\res
com.example.myapplication.app-ui-tooling-data-release-53 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f052b559cbc4c7f12fcd6794b5bef1e0\transformed\ui-tooling-data-release\res
com.example.myapplication.app-material-ripple-release-54 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f07c7f5fd6d886868408f5a3c2274fff\transformed\material-ripple-release\res
com.example.myapplication.app-ui-release-55 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f11933d2f9c817336fdb6ff83a5031f8\transformed\ui-release\res
com.example.myapplication.app-activity-compose-1.10.1-56 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c3c6de17e5be39a84fb800efecd44f\transformed\activity-compose-1.10.1\res
com.example.myapplication.app-sqlite-2.4.0-57 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7382175c7eb930a9a446c50c45ebc53\transformed\sqlite-2.4.0\res
com.example.myapplication.app-navigation-runtime-ktx-2.7.5-58 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc139c91dac3bacf1c471d2cfc1f5d3b\transformed\navigation-runtime-ktx-2.7.5\res
com.example.myapplication.app-pngs-59 C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\build\generated\res\pngs\debug
com.example.myapplication.app-resValues-60 C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\build\generated\res\resValues\debug
com.example.myapplication.app-packageDebugResources-61 C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.myapplication.app-packageDebugResources-62 C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.myapplication.app-debug-63 C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.myapplication.app-debug-64 C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\debug\res
com.example.myapplication.app-main-65 C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\res
