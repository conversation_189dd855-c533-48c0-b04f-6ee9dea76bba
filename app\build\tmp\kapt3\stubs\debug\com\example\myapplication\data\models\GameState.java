package com.example.myapplication.data.models;

/**
 * 游戏状态数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b<\n\u0002\u0010\u0002\n\u0002\b\u0018\n\u0002\u0010\u0000\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0087\b\u0018\u0000 \u0085\u00012\u00020\u0001:\u0002\u0085\u0001B\u00cf\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u0012\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n\u0012\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\n\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u001d\u00a2\u0006\u0002\u0010\u001eJ\u0006\u0010Y\u001a\u00020ZJ\t\u0010[\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\\\u001a\u00020\u0012H\u00c6\u0003J\t\u0010]\u001a\u00020\u0007H\u00c6\u0003J\t\u0010^\u001a\u00020\u0015H\u00c6\u0003J\t\u0010_\u001a\u00020\u0015H\u00c6\u0003J\t\u0010`\u001a\u00020\u0007H\u00c6\u0003J\t\u0010a\u001a\u00020\u0007H\u00c6\u0003J\t\u0010b\u001a\u00020\u0007H\u00c6\u0003J\t\u0010c\u001a\u00020\u0007H\u00c6\u0003J\t\u0010d\u001a\u00020\u0007H\u00c6\u0003J\t\u0010e\u001a\u00020\u001dH\u00c6\u0003J\t\u0010f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010g\u001a\u00020\u0007H\u00c6\u0003J\t\u0010h\u001a\u00020\u0007H\u00c6\u0003J\u000f\u0010i\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00c6\u0003J\u000f\u0010j\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00c6\u0003J\u000b\u0010k\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\t\u0010l\u001a\u00020\u0007H\u00c6\u0003J\t\u0010m\u001a\u00020\u0007H\u00c6\u0003J\u00d5\u0001\u0010n\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00072\b\b\u0002\u0010\u0010\u001a\u00020\u00072\b\b\u0002\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u00072\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u00152\b\b\u0002\u0010\u0017\u001a\u00020\u00072\b\b\u0002\u0010\u0018\u001a\u00020\u00072\b\b\u0002\u0010\u0019\u001a\u00020\u00072\b\b\u0002\u0010\u001a\u001a\u00020\u00072\b\b\u0002\u0010\u001b\u001a\u00020\u00072\b\b\u0002\u0010\u001c\u001a\u00020\u001dH\u00c6\u0001J\t\u0010o\u001a\u00020\u0007H\u00d6\u0001J\u0016\u0010p\u001a\u00020Z2\u0006\u0010\u0013\u001a\u00020\u00072\u0006\u0010\u0011\u001a\u00020\u0012J\u0013\u0010q\u001a\u00020\u001d2\b\u0010r\u001a\u0004\u0018\u00010sH\u00d6\u0003J\t\u0010t\u001a\u00020\u0007H\u00d6\u0001J\u0010\u0010u\u001a\u00020Z2\b\b\u0002\u0010v\u001a\u00020\u0007J\u0006\u0010w\u001a\u00020ZJ$\u0010x\u001a\u00020Z2\u0006\u0010y\u001a\u00020\u00072\f\u0010z\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010{\u001a\u00020\u000eJ\u0006\u0010|\u001a\u00020ZJ\u001c\u0010}\u001a\u00020Z2\u0006\u0010y\u001a\u00020\u00072\f\u0010~\u001a\b\u0012\u0004\u0012\u00020\u000b0\nJ\u0006\u0010\u007f\u001a\u00020ZJ\n\u0010\u0080\u0001\u001a\u00020\u0003H\u00d6\u0001J\u001d\u0010\u0081\u0001\u001a\u00020Z2\b\u0010\u0082\u0001\u001a\u00030\u0083\u00012\u0007\u0010\u0084\u0001\u001a\u00020\u0007H\u00d6\u0001R\u001a\u0010\u001b\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001f\u0010 \"\u0004\b!\u0010\"R\u001a\u0010\u001a\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b#\u0010 \"\u0004\b$\u0010\"R\u001a\u0010\u0018\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b%\u0010 \"\u0004\b&\u0010\"R\u001a\u0010\u0006\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010 \"\u0004\b(\u0010\"R\u0011\u0010)\u001a\u00020\u00158F\u00a2\u0006\u0006\u001a\u0004\b*\u0010+R\u001a\u0010\u0016\u001a\u00020\u0015X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010+\"\u0004\b-\u0010.R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u001a\u0010\u0014\u001a\u00020\u0015X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b1\u0010+\"\u0004\b2\u0010.R\u0011\u00103\u001a\u00020\u001d8F\u00a2\u0006\u0006\u001a\u0004\b4\u00105R\u0011\u00106\u001a\u00020\u001d8F\u00a2\u0006\u0006\u001a\u0004\b6\u00105R\u0011\u00107\u001a\u00020\u001d8F\u00a2\u0006\u0006\u001a\u0004\b7\u00105R\u0011\u00108\u001a\u00020\u001d8F\u00a2\u0006\u0006\u001a\u0004\b8\u00105R\u001a\u0010\u001c\u001a\u00020\u001dX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001c\u00105\"\u0004\b9\u0010:R \u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b;\u0010<\"\u0004\b=\u0010>R\u001a\u0010\b\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b?\u0010 \"\u0004\b@\u0010\"R \u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bA\u0010<\"\u0004\bB\u0010>R\u001c\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bC\u0010D\"\u0004\bE\u0010FR\u001a\u0010\u000f\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bG\u0010 \"\u0004\bH\u0010\"R\u001a\u0010\u0019\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bI\u0010 \"\u0004\bJ\u0010\"R\u001a\u0010\u0010\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bK\u0010 \"\u0004\bL\u0010\"R\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bM\u0010N\"\u0004\bO\u0010PR\u001a\u0010\u0011\u001a\u00020\u0012X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bQ\u0010R\"\u0004\bS\u0010TR\u001a\u0010\u0017\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bU\u0010 \"\u0004\bV\u0010\"R\u001a\u0010\u0013\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bW\u0010 \"\u0004\bX\u0010\"\u00a8\u0006\u0086\u0001"}, d2 = {"Lcom/example/myapplication/data/models/GameState;", "Landroid/os/Parcelable;", "gameId", "", "phase", "Lcom/example/myapplication/data/models/GamePhase;", "currentPlayerIndex", "", "landlordIndex", "landlordCards", "", "Lcom/example/myapplication/data/models/Card;", "lastPlayedCards", "lastPlayedPattern", "Lcom/example/myapplication/data/models/CardPattern;", "lastPlayerIndex", "passCount", "result", "Lcom/example/myapplication/data/models/GameResult;", "winnerIndex", "gameStartTime", "", "gameEndTime", "roundNumber", "callingRound", "maxCallingScore", "callingPlayerIndex", "bombCount", "isSpring", "", "(Ljava/lang/String;Lcom/example/myapplication/data/models/GamePhase;IILjava/util/List;Ljava/util/List;Lcom/example/myapplication/data/models/CardPattern;IILcom/example/myapplication/data/models/GameResult;IJJIIIIIZ)V", "getBombCount", "()I", "setBombCount", "(I)V", "getCallingPlayerIndex", "setCallingPlayerIndex", "getCallingRound", "setCallingRound", "getCurrentPlayerIndex", "setCurrentPlayerIndex", "gameDuration", "getGameDuration", "()J", "getGameEndTime", "setGameEndTime", "(J)V", "getGameId", "()Ljava/lang/String;", "getGameStartTime", "setGameStartTime", "hasLandlord", "getHasLandlord", "()Z", "isCallingPhase", "isGameActive", "isGameOver", "setSpring", "(Z)V", "getLandlordCards", "()Ljava/util/List;", "setLandlordCards", "(Ljava/util/List;)V", "getLandlordIndex", "setLandlordIndex", "getLastPlayedCards", "setLastPlayedCards", "getLastPlayedPattern", "()Lcom/example/myapplication/data/models/CardPattern;", "setLastPlayedPattern", "(Lcom/example/myapplication/data/models/CardPattern;)V", "getLastPlayerIndex", "setLastPlayerIndex", "getMaxCallingScore", "setMaxCallingScore", "getPassCount", "setPassCount", "getPhase", "()Lcom/example/myapplication/data/models/GamePhase;", "setPhase", "(Lcom/example/myapplication/data/models/GamePhase;)V", "getResult", "()Lcom/example/myapplication/data/models/GameResult;", "setResult", "(Lcom/example/myapplication/data/models/GameResult;)V", "getRoundNumber", "setRoundNumber", "getWinnerIndex", "setWinnerIndex", "clearLastPlay", "", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "describeContents", "endGame", "equals", "other", "", "hashCode", "nextPlayer", "playerCount", "pass", "playCards", "playerIndex", "cards", "pattern", "reset", "setLandlord", "bottomCards", "startNewGame", "toString", "writeToParcel", "parcel", "Landroid/os/Parcel;", "flags", "Companion", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class GameState implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String gameId = null;
    @org.jetbrains.annotations.NotNull()
    private com.example.myapplication.data.models.GamePhase phase;
    private int currentPlayerIndex;
    private int landlordIndex;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.example.myapplication.data.models.Card> landlordCards;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.example.myapplication.data.models.Card> lastPlayedCards;
    @org.jetbrains.annotations.Nullable()
    private com.example.myapplication.data.models.CardPattern lastPlayedPattern;
    private int lastPlayerIndex;
    private int passCount;
    @org.jetbrains.annotations.NotNull()
    private com.example.myapplication.data.models.GameResult result;
    private int winnerIndex;
    private long gameStartTime;
    private long gameEndTime;
    private int roundNumber;
    private int callingRound;
    private int maxCallingScore;
    private int callingPlayerIndex;
    private int bombCount;
    private boolean isSpring;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.myapplication.data.models.GameState.Companion Companion = null;
    
    public GameState(@org.jetbrains.annotations.NotNull()
    java.lang.String gameId, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GamePhase phase, int currentPlayerIndex, int landlordIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> landlordCards, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> lastPlayedCards, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.models.CardPattern lastPlayedPattern, int lastPlayerIndex, int passCount, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GameResult result, int winnerIndex, long gameStartTime, long gameEndTime, int roundNumber, int callingRound, int maxCallingScore, int callingPlayerIndex, int bombCount, boolean isSpring) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getGameId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.GamePhase getPhase() {
        return null;
    }
    
    public final void setPhase(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GamePhase p0) {
    }
    
    public final int getCurrentPlayerIndex() {
        return 0;
    }
    
    public final void setCurrentPlayerIndex(int p0) {
    }
    
    public final int getLandlordIndex() {
        return 0;
    }
    
    public final void setLandlordIndex(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.models.Card> getLandlordCards() {
        return null;
    }
    
    public final void setLandlordCards(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.models.Card> getLastPlayedCards() {
        return null;
    }
    
    public final void setLastPlayedCards(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.CardPattern getLastPlayedPattern() {
        return null;
    }
    
    public final void setLastPlayedPattern(@org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.models.CardPattern p0) {
    }
    
    public final int getLastPlayerIndex() {
        return 0;
    }
    
    public final void setLastPlayerIndex(int p0) {
    }
    
    public final int getPassCount() {
        return 0;
    }
    
    public final void setPassCount(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.GameResult getResult() {
        return null;
    }
    
    public final void setResult(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GameResult p0) {
    }
    
    public final int getWinnerIndex() {
        return 0;
    }
    
    public final void setWinnerIndex(int p0) {
    }
    
    public final long getGameStartTime() {
        return 0L;
    }
    
    public final void setGameStartTime(long p0) {
    }
    
    public final long getGameEndTime() {
        return 0L;
    }
    
    public final void setGameEndTime(long p0) {
    }
    
    public final int getRoundNumber() {
        return 0;
    }
    
    public final void setRoundNumber(int p0) {
    }
    
    public final int getCallingRound() {
        return 0;
    }
    
    public final void setCallingRound(int p0) {
    }
    
    public final int getMaxCallingScore() {
        return 0;
    }
    
    public final void setMaxCallingScore(int p0) {
    }
    
    public final int getCallingPlayerIndex() {
        return 0;
    }
    
    public final void setCallingPlayerIndex(int p0) {
    }
    
    public final int getBombCount() {
        return 0;
    }
    
    public final void setBombCount(int p0) {
    }
    
    public final boolean isSpring() {
        return false;
    }
    
    public final void setSpring(boolean p0) {
    }
    
    public final boolean getHasLandlord() {
        return false;
    }
    
    public final boolean isGameOver() {
        return false;
    }
    
    public final boolean isGameActive() {
        return false;
    }
    
    public final boolean isCallingPhase() {
        return false;
    }
    
    public final long getGameDuration() {
        return 0L;
    }
    
    /**
     * 切换到下一个玩家
     */
    public final void nextPlayer(int playerCount) {
    }
    
    /**
     * 设置地主
     */
    public final void setLandlord(int playerIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> bottomCards) {
    }
    
    /**
     * 玩家出牌
     */
    public final void playCards(int playerIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.CardPattern pattern) {
    }
    
    /**
     * 玩家过牌
     */
    public final void pass() {
    }
    
    /**
     * 清空上次出牌记录
     */
    public final void clearLastPlay() {
    }
    
    /**
     * 结束游戏
     */
    public final void endGame(int winnerIndex, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GameResult result) {
    }
    
    /**
     * 重置游戏状态
     */
    public final void reset() {
    }
    
    /**
     * 开始新游戏
     */
    public final void startNewGame() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.GameResult component10() {
        return null;
    }
    
    public final int component11() {
        return 0;
    }
    
    public final long component12() {
        return 0L;
    }
    
    public final long component13() {
        return 0L;
    }
    
    public final int component14() {
        return 0;
    }
    
    public final int component15() {
        return 0;
    }
    
    public final int component16() {
        return 0;
    }
    
    public final int component17() {
        return 0;
    }
    
    public final int component18() {
        return 0;
    }
    
    public final boolean component19() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.GamePhase component2() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final int component4() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.models.Card> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.models.Card> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.CardPattern component7() {
        return null;
    }
    
    public final int component8() {
        return 0;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.GameState copy(@org.jetbrains.annotations.NotNull()
    java.lang.String gameId, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GamePhase phase, int currentPlayerIndex, int landlordIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> landlordCards, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> lastPlayedCards, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.models.CardPattern lastPlayedPattern, int lastPlayerIndex, int passCount, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GameResult result, int winnerIndex, long gameStartTime, long gameEndTime, int roundNumber, int callingRound, int maxCallingScore, int callingPlayerIndex, int bombCount, boolean isSpring) {
        return null;
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/myapplication/data/models/GameState$Companion;", "", "()V", "newGame", "Lcom/example/myapplication/data/models/GameState;", "gameId", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 创建新游戏状态
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.GameState newGame(@org.jetbrains.annotations.NotNull()
        java.lang.String gameId) {
            return null;
        }
    }
}