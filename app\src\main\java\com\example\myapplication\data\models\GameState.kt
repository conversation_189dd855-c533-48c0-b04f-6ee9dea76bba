package com.example.myapplication.data.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

/**
 * 游戏阶段枚举
 */
enum class GamePhase {
    WAITING,        // 等待开始
    DEALING,        // 发牌阶段
    CALLING,        // 叫地主阶段
    PLAYING,        // 出牌阶段
    FINISHED        // 游戏结束
}

/**
 * 游戏结果枚举
 */
enum class GameResult {
    LANDLORD_WIN,   // 地主胜利
    FARMERS_WIN,    // 农民胜利
    ONGOING         // 游戏进行中
}

/**
 * 游戏状态数据类
 */
@Parcelize
data class GameState(
    val gameId: String,
    var phase: GamePhase = GamePhase.WAITING,
    var currentPlayerIndex: Int = 0,
    var landlordIndex: Int = -1,
    var landlordCards: List<Card> = emptyList(),  // 地主牌（3张底牌）
    var lastPlayedCards: List<Card> = emptyList(),
    var lastPlayedPattern: @RawValue CardPattern? = null,
    var lastPlayerIndex: Int = -1,
    var passCount: Int = 0,  // 连续过牌次数
    var result: GameResult = GameResult.ONGOING,
    var winnerIndex: Int = -1,
    var gameStartTime: Long = 0L,
    var gameEndTime: Long = 0L,
    var roundNumber: Int = 1,
    var callingRound: Int = 0,  // 叫地主轮数
    var maxCallingScore: Int = 0,  // 最高叫分
    var callingPlayerIndex: Int = -1,  // 当前叫分最高的玩家
    var bombCount: Int = 0,  // 炸弹数量（用于计分）
    var isSpring: Boolean = false  // 是否春天
) : Parcelable {
    
    /**
     * 是否有地主
     */
    val hasLandlord: Boolean
        get() = landlordIndex >= 0
    
    /**
     * 游戏是否结束
     */
    val isGameOver: Boolean
        get() = phase == GamePhase.FINISHED
    
    /**
     * 游戏是否进行中
     */
    val isGameActive: Boolean
        get() = phase == GamePhase.PLAYING
    
    /**
     * 是否在叫地主阶段
     */
    val isCallingPhase: Boolean
        get() = phase == GamePhase.CALLING
    
    /**
     * 获取游戏时长（毫秒）
     */
    val gameDuration: Long
        get() = if (gameEndTime > 0) gameEndTime - gameStartTime else 0L
    
    /**
     * 切换到下一个玩家
     */
    fun nextPlayer(playerCount: Int = 3) {
        currentPlayerIndex = (currentPlayerIndex + 1) % playerCount
    }
    
    /**
     * 设置地主
     */
    fun setLandlord(playerIndex: Int, bottomCards: List<Card>) {
        landlordIndex = playerIndex
        landlordCards = bottomCards
        phase = GamePhase.PLAYING
        currentPlayerIndex = playerIndex  // 地主先出牌
    }
    
    /**
     * 玩家出牌
     */
    fun playCards(playerIndex: Int, cards: List<Card>, pattern: CardPattern) {
        lastPlayedCards = cards
        lastPlayedPattern = pattern
        lastPlayerIndex = playerIndex
        passCount = 0
        
        // 如果是炸弹，增加炸弹计数
        if (pattern.isBomb) {
            bombCount++
        }
    }
    
    /**
     * 玩家过牌
     */
    fun pass() {
        passCount++
        // 如果连续两个玩家过牌，清空上次出牌记录
        if (passCount >= 2) {
            clearLastPlay()
        }
    }
    
    /**
     * 清空上次出牌记录
     */
    fun clearLastPlay() {
        lastPlayedCards = emptyList()
        lastPlayedPattern = null
        lastPlayerIndex = -1
        passCount = 0
    }
    
    /**
     * 结束游戏
     */
    fun endGame(winnerIndex: Int, result: GameResult) {
        this.winnerIndex = winnerIndex
        this.result = result
        this.phase = GamePhase.FINISHED
        this.gameEndTime = System.currentTimeMillis()
    }
    
    /**
     * 重置游戏状态
     */
    fun reset() {
        phase = GamePhase.WAITING
        currentPlayerIndex = 0
        landlordIndex = -1
        landlordCards = emptyList()
        lastPlayedCards = emptyList()
        lastPlayedPattern = null
        lastPlayerIndex = -1
        passCount = 0
        result = GameResult.ONGOING
        winnerIndex = -1
        gameStartTime = 0L
        gameEndTime = 0L
        roundNumber = 1
        callingRound = 0
        maxCallingScore = 0
        callingPlayerIndex = -1
        bombCount = 0
        isSpring = false
    }
    
    /**
     * 开始新游戏
     */
    fun startNewGame() {
        reset()
        gameStartTime = System.currentTimeMillis()
        phase = GamePhase.DEALING
    }
    
    companion object {
        /**
         * 创建新游戏状态
         */
        fun newGame(gameId: String = "game_${System.currentTimeMillis()}"): GameState {
            return GameState(gameId = gameId).apply {
                startNewGame()
            }
        }
    }
}
