package com.example.myapplication.data.models;

/**
 * 牌型枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0015\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015j\u0002\b\u0016j\u0002\b\u0017j\u0002\b\u0018j\u0002\b\u0019\u00a8\u0006\u001a"}, d2 = {"Lcom/example/myapplication/data/models/CardPatternType;", "", "displayName", "", "priority", "", "(Ljava/lang/String;ILjava/lang/String;I)V", "getDisplayName", "()Ljava/lang/String;", "getPriority", "()I", "INVALID", "SINGLE", "PAIR", "TRIPLE", "TRIPLE_WITH_SINGLE", "TRIPLE_WITH_PAIR", "STRAIGHT", "PAIR_STRAIGHT", "TRIPLE_STRAIGHT", "TRIPLE_STRAIGHT_WITH_SINGLES", "TRIPLE_STRAIGHT_WITH_PAIRS", "FOUR_WITH_TWO_SINGLES", "FOUR_WITH_TWO_PAIRS", "BOMB", "ROCKET", "app_debug"})
public enum CardPatternType {
    /*public static final*/ INVALID /* = new INVALID(null, 0) */,
    /*public static final*/ SINGLE /* = new SINGLE(null, 0) */,
    /*public static final*/ PAIR /* = new PAIR(null, 0) */,
    /*public static final*/ TRIPLE /* = new TRIPLE(null, 0) */,
    /*public static final*/ TRIPLE_WITH_SINGLE /* = new TRIPLE_WITH_SINGLE(null, 0) */,
    /*public static final*/ TRIPLE_WITH_PAIR /* = new TRIPLE_WITH_PAIR(null, 0) */,
    /*public static final*/ STRAIGHT /* = new STRAIGHT(null, 0) */,
    /*public static final*/ PAIR_STRAIGHT /* = new PAIR_STRAIGHT(null, 0) */,
    /*public static final*/ TRIPLE_STRAIGHT /* = new TRIPLE_STRAIGHT(null, 0) */,
    /*public static final*/ TRIPLE_STRAIGHT_WITH_SINGLES /* = new TRIPLE_STRAIGHT_WITH_SINGLES(null, 0) */,
    /*public static final*/ TRIPLE_STRAIGHT_WITH_PAIRS /* = new TRIPLE_STRAIGHT_WITH_PAIRS(null, 0) */,
    /*public static final*/ FOUR_WITH_TWO_SINGLES /* = new FOUR_WITH_TWO_SINGLES(null, 0) */,
    /*public static final*/ FOUR_WITH_TWO_PAIRS /* = new FOUR_WITH_TWO_PAIRS(null, 0) */,
    /*public static final*/ BOMB /* = new BOMB(null, 0) */,
    /*public static final*/ ROCKET /* = new ROCKET(null, 0) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    private final int priority = 0;
    
    CardPatternType(java.lang.String displayName, int priority) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    public final int getPriority() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.myapplication.data.models.CardPatternType> getEntries() {
        return null;
    }
}