package com.example.myapplication.data.models;

/**
 * 扑克牌数据类
 * @param suit 花色
 * @param rank 点数
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000f\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\b\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0087\b\u0018\u0000 (2\u00020\u00012\b\u0012\u0004\u0012\u00020\u00000\u0002:\u0001(B\u0015\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\u0011\u0010\u0019\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u0000H\u0096\u0002J\t\u0010\u001b\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0006H\u00c6\u0003J\u001d\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u00c6\u0001J\t\u0010\u001e\u001a\u00020\u0016H\u00d6\u0001J\u0013\u0010\u001f\u001a\u00020\r2\b\u0010\u001a\u001a\u0004\u0018\u00010 H\u00d6\u0003J\t\u0010!\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\"\u001a\u00020\tH\u00d6\u0001J\u0019\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020\u0016H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\t8F\u00a2\u0006\u0006\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\f\u001a\u00020\r8F\u00a2\u0006\u0006\u001a\u0004\b\f\u0010\u000eR\u0011\u0010\u000f\u001a\u00020\r8F\u00a2\u0006\u0006\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\u0010\u001a\u00020\r8F\u00a2\u0006\u0006\u001a\u0004\b\u0010\u0010\u000eR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0015\u001a\u00020\u00168F\u00a2\u0006\u0006\u001a\u0004\b\u0017\u0010\u0018\u00a8\u0006)"}, d2 = {"Lcom/example/myapplication/data/models/Card;", "Landroid/os/Parcelable;", "", "suit", "Lcom/example/myapplication/data/models/Suit;", "rank", "Lcom/example/myapplication/data/models/Rank;", "(Lcom/example/myapplication/data/models/Suit;Lcom/example/myapplication/data/models/Rank;)V", "displayName", "", "getDisplayName", "()Ljava/lang/String;", "isBigJoker", "", "()Z", "isJoker", "isSmallJoker", "getRank", "()Lcom/example/myapplication/data/models/Rank;", "getSuit", "()Lcom/example/myapplication/data/models/Suit;", "weight", "", "getWeight", "()I", "compareTo", "other", "component1", "component2", "copy", "describeContents", "equals", "", "hashCode", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "Companion", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class Card implements android.os.Parcelable, java.lang.Comparable<com.example.myapplication.data.models.Card> {
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.models.Suit suit = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.models.Rank rank = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.myapplication.data.models.Card.Companion Companion = null;
    
    public Card(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.Suit suit, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.Rank rank) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.Suit getSuit() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.Rank getRank() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    public final int getWeight() {
        return 0;
    }
    
    public final boolean isJoker() {
        return false;
    }
    
    public final boolean isBigJoker() {
        return false;
    }
    
    public final boolean isSmallJoker() {
        return false;
    }
    
    /**
     * 比较牌的大小
     */
    @java.lang.Override()
    public int compareTo(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.Card other) {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.Suit component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.Rank component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.Card copy(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.Suit suit, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.Rank rank) {
        return null;
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004J\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0007\u001a\u00020\b\u00a8\u0006\t"}, d2 = {"Lcom/example/myapplication/data/models/Card$Companion;", "", "()V", "createFullDeck", "", "Lcom/example/myapplication/data/models/Card;", "fromString", "cardStr", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 创建一副完整的54张牌
         */
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.myapplication.data.models.Card> createFullDeck() {
            return null;
        }
        
        /**
         * 根据字符串创建牌（用于测试）
         */
        @org.jetbrains.annotations.Nullable()
        public final com.example.myapplication.data.models.Card fromString(@org.jetbrains.annotations.NotNull()
        java.lang.String cardStr) {
            return null;
        }
    }
}