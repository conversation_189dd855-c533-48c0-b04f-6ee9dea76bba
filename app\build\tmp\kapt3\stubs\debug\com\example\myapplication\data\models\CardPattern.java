package com.example.myapplication.data.models;

/**
 * 牌型数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0014\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0087\b\u0018\u0000 %2\u00020\u0001:\u0001%B1\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\u0010\u0010\u001a\u001a\u00020\u000f2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0000J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\nH\u00c6\u0003J9\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010!\u001a\u00020\u000f2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\"\u001a\u00020\nH\u00d6\u0001J\t\u0010#\u001a\u00020$H\u00d6\u0001R\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u000e\u001a\u00020\u000f8F\u00a2\u0006\u0006\u001a\u0004\b\u000e\u0010\u0010R\u0011\u0010\u0011\u001a\u00020\u000f8F\u00a2\u0006\u0006\u001a\u0004\b\u0011\u0010\u0010R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0018\u001a\u00020\n8F\u00a2\u0006\u0006\u001a\u0004\b\u0019\u0010\u0013\u00a8\u0006&"}, d2 = {"Lcom/example/myapplication/data/models/CardPattern;", "", "type", "Lcom/example/myapplication/data/models/CardPatternType;", "cards", "", "Lcom/example/myapplication/data/models/Card;", "mainRank", "Lcom/example/myapplication/data/models/Rank;", "length", "", "(Lcom/example/myapplication/data/models/CardPatternType;Ljava/util/List;Lcom/example/myapplication/data/models/Rank;I)V", "getCards", "()Ljava/util/List;", "isBomb", "", "()Z", "isValid", "getLength", "()I", "getMainRank", "()Lcom/example/myapplication/data/models/Rank;", "getType", "()Lcom/example/myapplication/data/models/CardPatternType;", "weight", "getWeight", "canBeat", "other", "component1", "component2", "component3", "component4", "copy", "equals", "hashCode", "toString", "", "Companion", "app_debug"})
public final class CardPattern {
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.models.CardPatternType type = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.myapplication.data.models.Card> cards = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.myapplication.data.models.Rank mainRank = null;
    private final int length = 0;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.myapplication.data.models.CardPattern.Companion Companion = null;
    
    public CardPattern(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.CardPatternType type, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.models.Rank mainRank, int length) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.CardPatternType getType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.models.Card> getCards() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.Rank getMainRank() {
        return null;
    }
    
    public final int getLength() {
        return 0;
    }
    
    public final int getWeight() {
        return 0;
    }
    
    public final boolean isValid() {
        return false;
    }
    
    public final boolean isBomb() {
        return false;
    }
    
    /**
     * 是否可以压过指定牌型
     */
    public final boolean canBeat(@org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.models.CardPattern other) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.CardPatternType component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.myapplication.data.models.Card> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.myapplication.data.models.Rank component3() {
        return null;
    }
    
    public final int component4() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.CardPattern copy(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.CardPatternType type, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards, @org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.models.Rank mainRank, int length) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0014\u0010\u0003\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0006\u0010\b\u001a\u00020\u0004J\u0014\u0010\t\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0014\u0010\n\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u000e\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\u0007J\u0014\u0010\r\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a8\u0006\u000e"}, d2 = {"Lcom/example/myapplication/data/models/CardPattern$Companion;", "", "()V", "bomb", "Lcom/example/myapplication/data/models/CardPattern;", "cards", "", "Lcom/example/myapplication/data/models/Card;", "invalid", "pair", "rocket", "single", "card", "triple", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 创建无效牌型
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.CardPattern invalid() {
            return null;
        }
        
        /**
         * 创建单牌
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.CardPattern single(@org.jetbrains.annotations.NotNull()
        com.example.myapplication.data.models.Card card) {
            return null;
        }
        
        /**
         * 创建对子
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.CardPattern pair(@org.jetbrains.annotations.NotNull()
        java.util.List<com.example.myapplication.data.models.Card> cards) {
            return null;
        }
        
        /**
         * 创建三张
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.CardPattern triple(@org.jetbrains.annotations.NotNull()
        java.util.List<com.example.myapplication.data.models.Card> cards) {
            return null;
        }
        
        /**
         * 创建炸弹
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.CardPattern bomb(@org.jetbrains.annotations.NotNull()
        java.util.List<com.example.myapplication.data.models.Card> cards) {
            return null;
        }
        
        /**
         * 创建王炸
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.CardPattern rocket(@org.jetbrains.annotations.NotNull()
        java.util.List<com.example.myapplication.data.models.Card> cards) {
            return null;
        }
    }
}