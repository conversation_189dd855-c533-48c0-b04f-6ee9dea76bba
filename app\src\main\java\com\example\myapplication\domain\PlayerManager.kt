package com.example.myapplication.domain

import com.example.myapplication.data.models.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 玩家管理器
 * 负责管理玩家状态、手牌、出牌记录等
 */
class PlayerManager {
    
    // 玩家列表
    private val _players = MutableStateFlow<List<Player>>(emptyList())
    val players: StateFlow<List<Player>> = _players.asStateFlow()
    
    // 当前活跃玩家索引
    private val _currentPlayerIndex = MutableStateFlow(0)
    val currentPlayerIndex: StateFlow<Int> = _currentPlayerIndex.asStateFlow()
    
    // 地主索引
    private val _landlordIndex = MutableStateFlow(-1)
    val landlordIndex: StateFlow<Int> = _landlordIndex.asStateFlow()
    
    /**
     * 初始化玩家
     */
    fun initializePlayers(
        humanPlayerName: String = "玩家",
        ai1Difficulty: PlayerType = PlayerType.AI_MEDIUM,
        ai2Difficulty: PlayerType = PlayerType.AI_MEDIUM
    ) {
        val playerList = listOf(
            Player.createHuman(humanPlayerName),
            Player.createAI("ai1", "电脑1", ai1Difficulty, PlayerPosition.LEFT),
            Player.createAI("ai2", "电脑2", ai2Difficulty, PlayerPosition.RIGHT)
        )
        
        _players.value = playerList
        _currentPlayerIndex.value = 0
        _landlordIndex.value = -1
    }
    
    /**
     * 获取指定索引的玩家
     */
    fun getPlayer(index: Int): Player? {
        return if (index in 0 until _players.value.size) {
            _players.value[index]
        } else null
    }
    
    /**
     * 获取当前玩家
     */
    fun getCurrentPlayer(): Player? {
        return getPlayer(_currentPlayerIndex.value)
    }
    
    /**
     * 获取地主玩家
     */
    fun getLandlordPlayer(): Player? {
        return if (_landlordIndex.value >= 0) {
            getPlayer(_landlordIndex.value)
        } else null
    }
    
    /**
     * 获取农民玩家列表
     */
    fun getFarmerPlayers(): List<Player> {
        return _players.value.filter { it.role == PlayerRole.FARMER }
    }
    
    /**
     * 获取人类玩家
     */
    fun getHumanPlayer(): Player? {
        return _players.value.find { it.isHuman }
    }
    
    /**
     * 获取AI玩家列表
     */
    fun getAIPlayers(): List<Player> {
        return _players.value.filter { it.isAI }
    }
    
    /**
     * 切换到下一个玩家
     */
    fun nextPlayer() {
        _currentPlayerIndex.value = (_currentPlayerIndex.value + 1) % _players.value.size
    }
    
    /**
     * 设置当前玩家
     */
    fun setCurrentPlayer(index: Int) {
        if (index in 0 until _players.value.size) {
            _currentPlayerIndex.value = index
        }
    }
    
    /**
     * 设置地主
     */
    fun setLandlord(playerIndex: Int, landlordCards: List<Card>) {
        if (playerIndex in 0 until _players.value.size) {
            _landlordIndex.value = playerIndex
            
            // 更新玩家角色
            _players.value.forEachIndexed { index, player ->
                player.role = if (index == playerIndex) {
                    PlayerRole.LANDLORD
                } else {
                    PlayerRole.FARMER
                }
            }
            
            // 地主获得底牌
            getPlayer(playerIndex)?.addCards(landlordCards)
            
            // 地主先出牌
            setCurrentPlayer(playerIndex)
        }
    }
    
    /**
     * 玩家叫地主
     */
    fun callLandlord(playerIndex: Int, score: Int): Boolean {
        val player = getPlayer(playerIndex) ?: return false
        
        if (player.hasCalledLandlord) return false
        
        player.hasCalledLandlord = true
        return true
    }
    
    /**
     * 玩家出牌
     */
    fun playCards(playerIndex: Int, cards: List<Card>): Boolean {
        val player = getPlayer(playerIndex) ?: return false
        return player.playCards(cards)
    }
    
    /**
     * 玩家过牌
     */
    fun pass(playerIndex: Int) {
        val player = getPlayer(playerIndex)
        player?.passCount = (player?.passCount ?: 0) + 1
    }
    
    /**
     * 检查是否有玩家获胜
     */
    fun checkWinner(): Player? {
        return _players.value.find { it.hasWon }
    }
    
    /**
     * 获取玩家手牌数量
     */
    fun getPlayerHandSizes(): List<Int> {
        return _players.value.map { it.handSize }
    }
    
    /**
     * 获取玩家最后出的牌
     */
    fun getLastPlayedCards(playerIndex: Int): List<Card>? {
        return getPlayer(playerIndex)?.getLastPlayedCards()
    }
    
    /**
     * 重置所有玩家状态
     */
    fun resetPlayers() {
        _players.value.forEach { it.reset() }
        _currentPlayerIndex.value = 0
        _landlordIndex.value = -1
    }
    
    /**
     * 计算玩家得分
     */
    fun calculateScores(
        baseScore: Int,
        bombCount: Int,
        isSpring: Boolean,
        winnerRole: PlayerRole
    ) {
        val bombMultiplier = Math.pow(2.0, bombCount.toDouble()).toInt()
        val springMultiplier = if (isSpring) 2 else 1
        val finalScore = baseScore * bombMultiplier * springMultiplier
        
        _players.value.forEach { player ->
            player.score = when {
                winnerRole == PlayerRole.LANDLORD && player.isLandlord -> finalScore * 2
                winnerRole == PlayerRole.LANDLORD && player.isFarmer -> -finalScore
                winnerRole == PlayerRole.FARMER && player.isLandlord -> -finalScore * 2
                winnerRole == PlayerRole.FARMER && player.isFarmer -> finalScore
                else -> 0
            }
        }
    }
    
    /**
     * 获取玩家统计信息
     */
    fun getPlayerStats(): Map<String, Any> {
        val humanPlayer = getHumanPlayer()
        val landlord = getLandlordPlayer()
        val farmers = getFarmerPlayers()
        
        return mapOf(
            "totalPlayers" to _players.value.size,
            "humanPlayer" to (humanPlayer?.name ?: "未知"),
            "landlord" to (landlord?.name ?: "未确定"),
            "farmers" to farmers.map { it.name },
            "handSizes" to getPlayerHandSizes(),
            "scores" to _players.value.map { it.score }
        )
    }
    
    /**
     * 验证玩家是否可以出指定的牌
     */
    fun canPlayCards(playerIndex: Int, cards: List<Card>): Boolean {
        val player = getPlayer(playerIndex) ?: return false
        return player.hand.containsAll(cards)
    }
    
    /**
     * 获取玩家在指定位置的相对索引
     */
    fun getPlayerIndexByPosition(position: PlayerPosition): Int? {
        return _players.value.indexOfFirst { it.position == position }.takeIf { it >= 0 }
    }
    
    /**
     * 获取下一个玩家索引
     */
    fun getNextPlayerIndex(currentIndex: Int): Int {
        return (currentIndex + 1) % _players.value.size
    }
    
    /**
     * 获取上一个玩家索引
     */
    fun getPreviousPlayerIndex(currentIndex: Int): Int {
        return (currentIndex - 1 + _players.value.size) % _players.value.size
    }
    
    /**
     * 检查是否所有玩家都已叫过地主
     */
    fun allPlayersCalledLandlord(): Boolean {
        return _players.value.all { it.hasCalledLandlord }
    }
    
    /**
     * 获取叫地主分数最高的玩家
     */
    fun getHighestCallingPlayer(callingScores: Map<Int, Int>): Int? {
        return callingScores.maxByOrNull { it.value }?.key
    }
}
