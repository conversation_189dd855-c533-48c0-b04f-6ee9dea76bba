package com.example.myapplication.ui.animation;

/**
 * 游戏动画组件
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0001!B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\nH\u0007J@\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\u0011\u0010\r\u001a\r\u0012\u0004\u0012\u00020\u00040\n\u00a2\u0006\u0002\b\u000e2\u0011\u0010\u000f\u001a\r\u0012\u0004\u0012\u00020\u00040\n\u00a2\u0006\u0002\b\u000eH\u0007J-\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u0011\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\u0011\u0010\u0012\u001a\r\u0012\u0004\u0012\u00020\u00040\n\u00a2\u0006\u0002\b\u000eH\u0007J-\u0010\u0013\u001a\u00020\u00042\u0006\u0010\u0014\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\u0011\u0010\u0012\u001a\r\u0012\u0004\u0012\u00020\u00040\n\u00a2\u0006\u0002\b\u000eH\u0007J7\u0010\u0015\u001a\u00020\u00042\u0006\u0010\u0016\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\u0017\u001a\u00020\u00182\u0011\u0010\u0012\u001a\r\u0012\u0004\u0012\u00020\u00040\n\u00a2\u0006\u0002\b\u000eH\u0007J-\u0010\u0019\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\u0011\u0010\u0012\u001a\r\u0012\u0004\u0012\u00020\u00040\n\u00a2\u0006\u0002\b\u000eH\u0007J-\u0010\u001b\u001a\u00020\u00042\u0006\u0010\u001c\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\u0011\u0010\u0012\u001a\r\u0012\u0004\u0012\u00020\u00040\n\u00a2\u0006\u0002\b\u000eH\u0007J7\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u0016\u001a\u00020\u00062\b\b\u0002\u0010\u001e\u001a\u00020\u001f2\b\b\u0002\u0010\u0007\u001a\u00020\b2\u0011\u0010\u0012\u001a\r\u0012\u0004\u0012\u00020\u00040\n\u00a2\u0006\u0002\b\u000eH\u0007J\u001a\u0010 \u001a\u00020\u00042\u0006\u0010\u0016\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u00a8\u0006\""}, d2 = {"Lcom/example/myapplication/ui/animation/GameAnimations;", "", "()V", "BombExplosionAnimation", "", "isExploding", "", "modifier", "Landroidx/compose/ui/Modifier;", "onAnimationEnd", "Lkotlin/Function0;", "CardFlipAnimation", "isFlipped", "frontContent", "Landroidx/compose/runtime/Composable;", "backContent", "CardPlayAnimation", "isPlaying", "content", "DealingAnimation", "isDealing", "FadeInOutAnimation", "isVisible", "durationMillis", "", "PulseAnimation", "isActive", "ShakeAnimation", "isShaking", "SlideInAnimation", "direction", "Lcom/example/myapplication/ui/animation/GameAnimations$SlideDirection;", "VictoryAnimation", "SlideDirection", "app_debug"})
public final class GameAnimations {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.myapplication.ui.animation.GameAnimations INSTANCE = null;
    
    private GameAnimations() {
        super();
    }
    
    /**
     * 卡牌翻转动画
     */
    @androidx.compose.runtime.Composable()
    public final void CardFlipAnimation(boolean isFlipped, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> frontContent, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> backContent) {
    }
    
    /**
     * 卡牌出牌动画
     */
    @androidx.compose.runtime.Composable()
    public final void CardPlayAnimation(boolean isPlaying, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 炸弹爆炸动画
     */
    @androidx.compose.runtime.Composable()
    public final void BombExplosionAnimation(boolean isExploding, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAnimationEnd) {
    }
    
    /**
     * 胜利庆祝动画
     */
    @androidx.compose.runtime.Composable()
    public final void VictoryAnimation(boolean isVisible, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 发牌动画
     */
    @androidx.compose.runtime.Composable()
    public final void DealingAnimation(boolean isDealing, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 脉冲动画（用于强调当前玩家）
     */
    @androidx.compose.runtime.Composable()
    public final void PulseAnimation(boolean isActive, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 摇摆动画（用于警告）
     */
    @androidx.compose.runtime.Composable()
    public final void ShakeAnimation(boolean isShaking, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 淡入淡出动画
     */
    @androidx.compose.runtime.Composable()
    public final void FadeInOutAnimation(boolean isVisible, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, int durationMillis, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 滑动进入动画
     */
    @androidx.compose.runtime.Composable()
    public final void SlideInAnimation(boolean isVisible, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.animation.GameAnimations.SlideDirection direction, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/myapplication/ui/animation/GameAnimations$SlideDirection;", "", "(Ljava/lang/String;I)V", "FromLeft", "FromRight", "FromTop", "FromBottom", "app_debug"})
    public static enum SlideDirection {
        /*public static final*/ FromLeft /* = new FromLeft() */,
        /*public static final*/ FromRight /* = new FromRight() */,
        /*public static final*/ FromTop /* = new FromTop() */,
        /*public static final*/ FromBottom /* = new FromBottom() */;
        
        SlideDirection() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.myapplication.ui.animation.GameAnimations.SlideDirection> getEntries() {
            return null;
        }
    }
}