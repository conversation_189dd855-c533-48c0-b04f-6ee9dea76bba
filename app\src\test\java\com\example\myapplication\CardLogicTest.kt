package com.example.myapplication

import com.example.myapplication.data.models.*
import com.example.myapplication.domain.CardLogic
import org.junit.Test
import org.junit.Assert.*

/**
 * 扑克牌逻辑测试类
 */
class CardLogicTest {
    
    private val cardLogic = CardLogic()
    
    @Test
    fun testCreateFullDeck() {
        val deck = Card.createFullDeck()
        assertEquals("牌堆应该有54张牌", 54, deck.size)
        
        // 检查是否有大小王
        assertTrue("应该包含小王", deck.any { it.rank == Rank.SMALL_JOKER })
        assertTrue("应该包含大王", deck.any { it.rank == Rank.BIG_JOKER })
        
        // 检查每种花色是否有13张牌
        val normalSuits = listOf(Suit.SPADES, Suit.HEARTS, Suit.CLUBS, Suit.DIAMONDS)
        normalSuits.forEach { suit ->
            val suitCards = deck.filter { it.suit == suit && !it.isJoker }
            assertEquals("每种花色应该有13张牌", 13, suitCards.size)
        }
    }
    
    @Test
    fun testShuffleDeck() {
        val originalDeck = Card.createFullDeck()
        val shuffledDeck = cardLogic.shuffleDeck(originalDeck)
        
        assertEquals("洗牌后牌数不变", originalDeck.size, shuffledDeck.size)
        assertTrue("洗牌后包含所有原始牌", shuffledDeck.containsAll(originalDeck))
        
        // 洗牌后顺序应该不同（虽然有极小概率相同）
        val isDifferent = originalDeck.zip(shuffledDeck).any { (a, b) -> a != b }
        assertTrue("洗牌后顺序应该改变", isDifferent)
    }
    
    @Test
    fun testIdentifySingleCard() {
        val card = Card(Suit.SPADES, Rank.ACE)
        val pattern = cardLogic.identifyPattern(listOf(card))
        
        assertEquals("应该识别为单牌", CardPatternType.SINGLE, pattern.type)
        assertEquals("主牌点数应该是A", Rank.ACE, pattern.mainRank)
        assertTrue("应该是有效牌型", pattern.isValid)
    }
    
    @Test
    fun testIdentifyPair() {
        val cards = listOf(
            Card(Suit.SPADES, Rank.KING),
            Card(Suit.HEARTS, Rank.KING)
        )
        val pattern = cardLogic.identifyPattern(cards)
        
        assertEquals("应该识别为对子", CardPatternType.PAIR, pattern.type)
        assertEquals("主牌点数应该是K", Rank.KING, pattern.mainRank)
        assertTrue("应该是有效牌型", pattern.isValid)
    }
    
    @Test
    fun testIdentifyTriple() {
        val cards = listOf(
            Card(Suit.SPADES, Rank.QUEEN),
            Card(Suit.HEARTS, Rank.QUEEN),
            Card(Suit.CLUBS, Rank.QUEEN)
        )
        val pattern = cardLogic.identifyPattern(cards)
        
        assertEquals("应该识别为三张", CardPatternType.TRIPLE, pattern.type)
        assertEquals("主牌点数应该是Q", Rank.QUEEN, pattern.mainRank)
        assertTrue("应该是有效牌型", pattern.isValid)
    }
    
    @Test
    fun testIdentifyBomb() {
        val cards = listOf(
            Card(Suit.SPADES, Rank.JACK),
            Card(Suit.HEARTS, Rank.JACK),
            Card(Suit.CLUBS, Rank.JACK),
            Card(Suit.DIAMONDS, Rank.JACK)
        )
        val pattern = cardLogic.identifyPattern(cards)
        
        assertEquals("应该识别为炸弹", CardPatternType.BOMB, pattern.type)
        assertEquals("主牌点数应该是J", Rank.JACK, pattern.mainRank)
        assertTrue("应该是有效牌型", pattern.isValid)
        assertTrue("应该是炸弹类型", pattern.isBomb)
    }
    
    @Test
    fun testIdentifyRocket() {
        val cards = listOf(
            Card(Suit.JOKER, Rank.SMALL_JOKER),
            Card(Suit.JOKER, Rank.BIG_JOKER)
        )
        val pattern = cardLogic.identifyPattern(cards)
        
        assertEquals("应该识别为王炸", CardPatternType.ROCKET, pattern.type)
        assertEquals("主牌点数应该是大王", Rank.BIG_JOKER, pattern.mainRank)
        assertTrue("应该是有效牌型", pattern.isValid)
        assertTrue("应该是炸弹类型", pattern.isBomb)
    }
    
    @Test
    fun testIdentifyTripleWithSingle() {
        val cards = listOf(
            Card(Suit.SPADES, Rank.TEN),
            Card(Suit.HEARTS, Rank.TEN),
            Card(Suit.CLUBS, Rank.TEN),
            Card(Suit.DIAMONDS, Rank.FOUR)
        )
        val pattern = cardLogic.identifyPattern(cards)
        
        assertEquals("应该识别为三带一", CardPatternType.TRIPLE_WITH_SINGLE, pattern.type)
        assertEquals("主牌点数应该是10", Rank.TEN, pattern.mainRank)
        assertTrue("应该是有效牌型", pattern.isValid)
    }
    
    @Test
    fun testIdentifyTripleWithPair() {
        val cards = listOf(
            Card(Suit.SPADES, Rank.NINE),
            Card(Suit.HEARTS, Rank.NINE),
            Card(Suit.CLUBS, Rank.NINE),
            Card(Suit.DIAMONDS, Rank.FIVE),
            Card(Suit.SPADES, Rank.FIVE)
        )
        val pattern = cardLogic.identifyPattern(cards)
        
        assertEquals("应该识别为三带二", CardPatternType.TRIPLE_WITH_PAIR, pattern.type)
        assertEquals("主牌点数应该是9", Rank.NINE, pattern.mainRank)
        assertTrue("应该是有效牌型", pattern.isValid)
    }
    
    @Test
    fun testIdentifyStraight() {
        val cards = listOf(
            Card(Suit.SPADES, Rank.THREE),
            Card(Suit.HEARTS, Rank.FOUR),
            Card(Suit.CLUBS, Rank.FIVE),
            Card(Suit.DIAMONDS, Rank.SIX),
            Card(Suit.SPADES, Rank.SEVEN)
        )
        val pattern = cardLogic.identifyPattern(cards)
        
        assertEquals("应该识别为顺子", CardPatternType.STRAIGHT, pattern.type)
        assertEquals("主牌点数应该是7", Rank.SEVEN, pattern.mainRank)
        assertEquals("顺子长度应该是5", 5, pattern.length)
        assertTrue("应该是有效牌型", pattern.isValid)
    }
    
    @Test
    fun testInvalidPattern() {
        val cards = listOf(
            Card(Suit.SPADES, Rank.THREE),
            Card(Suit.HEARTS, Rank.FIVE),
            Card(Suit.CLUBS, Rank.SEVEN)
        )
        val pattern = cardLogic.identifyPattern(cards)
        
        assertEquals("应该识别为无效牌型", CardPatternType.INVALID, pattern.type)
        assertFalse("应该是无效牌型", pattern.isValid)
    }
    
    @Test
    fun testPatternComparison() {
        val smallPair = cardLogic.identifyPattern(listOf(
            Card(Suit.SPADES, Rank.THREE),
            Card(Suit.HEARTS, Rank.THREE)
        ))
        
        val bigPair = cardLogic.identifyPattern(listOf(
            Card(Suit.SPADES, Rank.KING),
            Card(Suit.HEARTS, Rank.KING)
        ))
        
        val bomb = cardLogic.identifyPattern(listOf(
            Card(Suit.SPADES, Rank.FOUR),
            Card(Suit.HEARTS, Rank.FOUR),
            Card(Suit.CLUBS, Rank.FOUR),
            Card(Suit.DIAMONDS, Rank.FOUR)
        ))
        
        val rocket = cardLogic.identifyPattern(listOf(
            Card(Suit.JOKER, Rank.SMALL_JOKER),
            Card(Suit.JOKER, Rank.BIG_JOKER)
        ))
        
        assertTrue("大对子应该能压过小对子", bigPair.canBeat(smallPair))
        assertFalse("小对子不能压过大对子", smallPair.canBeat(bigPair))
        assertTrue("炸弹应该能压过对子", bomb.canBeat(bigPair))
        assertFalse("对子不能压过炸弹", bigPair.canBeat(bomb))
        assertTrue("王炸应该能压过炸弹", rocket.canBeat(bomb))
        assertFalse("炸弹不能压过王炸", bomb.canBeat(rocket))
    }
    
    @Test
    fun testDealCards() {
        val deck = Card.createFullDeck()
        val shuffledDeck = cardLogic.shuffleDeck(deck)
        
        val players = listOf(
            Player.createHuman("玩家1"),
            Player.createAI("ai1", "电脑1", PlayerType.AI_EASY, PlayerPosition.LEFT),
            Player.createAI("ai2", "电脑2", PlayerType.AI_MEDIUM, PlayerPosition.RIGHT)
        )
        
        val bottomCards = cardLogic.dealCards(shuffledDeck, players)
        
        assertEquals("底牌应该有3张", 3, bottomCards.size)
        players.forEach { player ->
            assertEquals("每个玩家应该有17张牌", 17, player.handSize)
        }
        
        val totalCards = players.sumOf { it.handSize } + bottomCards.size
        assertEquals("总牌数应该是54", 54, totalCards)
    }
}
