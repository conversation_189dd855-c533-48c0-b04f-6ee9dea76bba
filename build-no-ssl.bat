@echo off
echo ========================================
echo 斗地主游戏构建脚本（禁用SSL验证）
echo ⚠️  警告：此脚本仅用于开发环境
echo ========================================

echo.
echo 🔒 禁用SSL验证设置...
set GRADLE_OPTS=-Djavax.net.ssl.trustStore=NONE -Djavax.net.ssl.trustStoreType=Windows-ROOT -Dcom.sun.net.ssl.checkRevocation=false -Dsun.security.ssl.allowUnsafeRenegotiation=true

echo.
echo 🧹 清理项目...
call gradlew clean --init-script gradle/init.gradle

echo.
echo 📦 检查依赖（禁用SSL）...
call gradlew dependencies --configuration implementation --init-script gradle/init.gradle

echo.
echo 🔨 构建Debug版本（禁用SSL验证）...
call gradlew assembleDebug --init-script gradle/init.gradle ^
    -Dorg.gradle.internal.http.connectionTimeout=300000 ^
    -Dorg.gradle.internal.http.socketTimeout=300000 ^
    --stacktrace

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功！
    echo 📱 APK位置: app\build\outputs\apk\debug\app-debug.apk
    
    echo.
    echo 🧪 运行测试...
    call gradlew test --init-script gradle/init.gradle
) else (
    echo.
    echo ❌ 构建失败，错误代码: %ERRORLEVEL%
    echo 💡 请检查网络连接或尝试以下解决方案：
    echo    1. 检查防火墙设置
    echo    2. 尝试使用VPN或代理
    echo    3. 使用移动热点网络
    echo    4. 联系网络管理员
)

echo.
echo ========================================
echo 构建完成
echo ========================================

pause
