package com.example.myapplication.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000.\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\u001aJ\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00032\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u001a\u0010\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\fH\u0007\u001a \u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0011H\u0007\u00a8\u0006\u0013"}, d2 = {"GameControlButtons", "", "canPlay", "", "canPass", "canPlaySelected", "onPlayCards", "Lkotlin/Function0;", "onPass", "onClearSelection", "GameScreen", "gameViewModel", "Lcom/example/myapplication/ui/viewmodel/GameViewModel;", "GameStatusBar", "gameState", "Lcom/example/myapplication/data/models/GameState;", "currentPlayerName", "", "lastPlayInfo", "app_debug"})
public final class GameScreenKt {
    
    /**
     * 游戏主界面
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void GameScreen(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.viewmodel.GameViewModel gameViewModel) {
    }
    
    /**
     * 游戏状态栏
     */
    @androidx.compose.runtime.Composable()
    public static final void GameStatusBar(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GameState gameState, @org.jetbrains.annotations.NotNull()
    java.lang.String currentPlayerName, @org.jetbrains.annotations.NotNull()
    java.lang.String lastPlayInfo) {
    }
    
    /**
     * 游戏控制按钮
     */
    @androidx.compose.runtime.Composable()
    public static final void GameControlButtons(boolean canPlay, boolean canPass, boolean canPlaySelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPlayCards, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPass, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClearSelection) {
    }
}