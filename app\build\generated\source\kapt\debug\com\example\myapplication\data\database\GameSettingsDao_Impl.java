package com.example.myapplication.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class GameSettingsDao_Impl implements GameSettingsDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<GameSettingsEntity> __insertionAdapterOfGameSettingsEntity;

  private final SharedSQLiteStatement __preparedStmtOfUpdatePlayerName;

  private final SharedSQLiteStatement __preparedStmtOfUpdateSoundEnabled;

  private final SharedSQLiteStatement __preparedStmtOfUpdateMusicEnabled;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAIDifficulty1;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAIDifficulty2;

  private final SharedSQLiteStatement __preparedStmtOfUpdateTheme;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAnimationEnabled;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAutoPlay;

  private final SharedSQLiteStatement __preparedStmtOfUpdateGameSpeed;

  public GameSettingsDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfGameSettingsEntity = new EntityInsertionAdapter<GameSettingsEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `game_settings` (`id`,`playerName`,`soundEnabled`,`musicEnabled`,`aiDifficulty1`,`aiDifficulty2`,`theme`,`animationEnabled`,`autoPlay`,`gameSpeed`) VALUES (?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GameSettingsEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPlayerName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPlayerName());
        }
        final int _tmp = entity.getSoundEnabled() ? 1 : 0;
        statement.bindLong(3, _tmp);
        final int _tmp_1 = entity.getMusicEnabled() ? 1 : 0;
        statement.bindLong(4, _tmp_1);
        if (entity.getAiDifficulty1() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAiDifficulty1());
        }
        if (entity.getAiDifficulty2() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getAiDifficulty2());
        }
        if (entity.getTheme() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getTheme());
        }
        final int _tmp_2 = entity.getAnimationEnabled() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        final int _tmp_3 = entity.getAutoPlay() ? 1 : 0;
        statement.bindLong(9, _tmp_3);
        statement.bindLong(10, entity.getGameSpeed());
      }
    };
    this.__preparedStmtOfUpdatePlayerName = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE game_settings SET playerName = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateSoundEnabled = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE game_settings SET soundEnabled = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateMusicEnabled = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE game_settings SET musicEnabled = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAIDifficulty1 = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE game_settings SET aiDifficulty1 = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAIDifficulty2 = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE game_settings SET aiDifficulty2 = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateTheme = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE game_settings SET theme = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAnimationEnabled = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE game_settings SET animationEnabled = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAutoPlay = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE game_settings SET autoPlay = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateGameSpeed = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE game_settings SET gameSpeed = ? WHERE id = 1";
        return _query;
      }
    };
  }

  @Override
  public Object saveSettings(final GameSettingsEntity settings,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfGameSettingsEntity.insert(settings);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updatePlayerName(final String name, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdatePlayerName.acquire();
        int _argIndex = 1;
        if (name == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, name);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdatePlayerName.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSoundEnabled(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateSoundEnabled.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateSoundEnabled.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateMusicEnabled(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateMusicEnabled.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateMusicEnabled.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAIDifficulty1(final String difficulty,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAIDifficulty1.acquire();
        int _argIndex = 1;
        if (difficulty == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, difficulty);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAIDifficulty1.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAIDifficulty2(final String difficulty,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAIDifficulty2.acquire();
        int _argIndex = 1;
        if (difficulty == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, difficulty);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAIDifficulty2.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTheme(final String theme, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateTheme.acquire();
        int _argIndex = 1;
        if (theme == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, theme);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateTheme.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAnimationEnabled(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAnimationEnabled.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAnimationEnabled.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAutoPlay(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAutoPlay.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAutoPlay.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateGameSpeed(final int speed, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateGameSpeed.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, speed);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateGameSpeed.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getSettings(final Continuation<? super GameSettingsEntity> $completion) {
    final String _sql = "SELECT * FROM game_settings WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<GameSettingsEntity>() {
      @Override
      @Nullable
      public GameSettingsEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPlayerName = CursorUtil.getColumnIndexOrThrow(_cursor, "playerName");
          final int _cursorIndexOfSoundEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "soundEnabled");
          final int _cursorIndexOfMusicEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "musicEnabled");
          final int _cursorIndexOfAiDifficulty1 = CursorUtil.getColumnIndexOrThrow(_cursor, "aiDifficulty1");
          final int _cursorIndexOfAiDifficulty2 = CursorUtil.getColumnIndexOrThrow(_cursor, "aiDifficulty2");
          final int _cursorIndexOfTheme = CursorUtil.getColumnIndexOrThrow(_cursor, "theme");
          final int _cursorIndexOfAnimationEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "animationEnabled");
          final int _cursorIndexOfAutoPlay = CursorUtil.getColumnIndexOrThrow(_cursor, "autoPlay");
          final int _cursorIndexOfGameSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "gameSpeed");
          final GameSettingsEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpPlayerName;
            if (_cursor.isNull(_cursorIndexOfPlayerName)) {
              _tmpPlayerName = null;
            } else {
              _tmpPlayerName = _cursor.getString(_cursorIndexOfPlayerName);
            }
            final boolean _tmpSoundEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSoundEnabled);
            _tmpSoundEnabled = _tmp != 0;
            final boolean _tmpMusicEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfMusicEnabled);
            _tmpMusicEnabled = _tmp_1 != 0;
            final String _tmpAiDifficulty1;
            if (_cursor.isNull(_cursorIndexOfAiDifficulty1)) {
              _tmpAiDifficulty1 = null;
            } else {
              _tmpAiDifficulty1 = _cursor.getString(_cursorIndexOfAiDifficulty1);
            }
            final String _tmpAiDifficulty2;
            if (_cursor.isNull(_cursorIndexOfAiDifficulty2)) {
              _tmpAiDifficulty2 = null;
            } else {
              _tmpAiDifficulty2 = _cursor.getString(_cursorIndexOfAiDifficulty2);
            }
            final String _tmpTheme;
            if (_cursor.isNull(_cursorIndexOfTheme)) {
              _tmpTheme = null;
            } else {
              _tmpTheme = _cursor.getString(_cursorIndexOfTheme);
            }
            final boolean _tmpAnimationEnabled;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfAnimationEnabled);
            _tmpAnimationEnabled = _tmp_2 != 0;
            final boolean _tmpAutoPlay;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfAutoPlay);
            _tmpAutoPlay = _tmp_3 != 0;
            final int _tmpGameSpeed;
            _tmpGameSpeed = _cursor.getInt(_cursorIndexOfGameSpeed);
            _result = new GameSettingsEntity(_tmpId,_tmpPlayerName,_tmpSoundEnabled,_tmpMusicEnabled,_tmpAiDifficulty1,_tmpAiDifficulty2,_tmpTheme,_tmpAnimationEnabled,_tmpAutoPlay,_tmpGameSpeed);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
