package com.example.myapplication.data.database;

/**
 * 类型转换器
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\tH\u0007J\u0010\u0010\n\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0007J\u0010\u0010\u000b\u001a\u00020\t2\u0006\u0010\b\u001a\u00020\u0004H\u0007\u00a8\u0006\f"}, d2 = {"Lcom/example/myapplication/data/database/Converters;", "", "()V", "fromGameResult", "", "result", "Lcom/example/myapplication/data/models/GameResult;", "fromPlayerType", "type", "Lcom/example/myapplication/data/models/PlayerType;", "toGameResult", "toPlayerType", "app_debug"})
public final class Converters {
    
    public Converters() {
        super();
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String fromPlayerType(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType type) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.PlayerType toPlayerType(@org.jetbrains.annotations.NotNull()
    java.lang.String type) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String fromGameResult(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GameResult result) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.GameResult toGameResult(@org.jetbrains.annotations.NotNull()
    java.lang.String result) {
        return null;
    }
}