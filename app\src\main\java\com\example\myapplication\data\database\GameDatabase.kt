package com.example.myapplication.data.database

import androidx.room.*
import androidx.room.Database
import com.example.myapplication.data.models.*

/**
 * 游戏历史记录实体
 */
@Entity(tableName = "game_history")
data class GameHistoryEntity(
    @PrimaryKey val gameId: String,
    val startTime: Long,
    val endTime: Long,
    val duration: Long,
    val result: String, // LANDLORD_WIN, FARMERS_WIN
    val winnerName: String,
    val playerNames: String, // JSON格式存储玩家名称
    val playerScores: String, // JSON格式存储玩家得分
    val bombCount: Int,
    val isSpring: Boolean,
    val landlordIndex: Int,
    val maxCallingScore: Int
)

/**
 * 游戏设置实体
 */
@Entity(tableName = "game_settings")
data class GameSettingsEntity(
    @PrimaryKey val id: Int = 1,
    val playerName: String,
    val soundEnabled: Boolean,
    val musicEnabled: Boolean,
    val aiDifficulty1: String, // AI_EASY, AI_MEDIUM, AI_HARD
    val aiDifficulty2: String,
    val theme: String, // LIGHT, DARK, AUTO
    val animationEnabled: Boolean,
    val autoPlay: Boolean,
    val gameSpeed: Int // 1-5
)

/**
 * 游戏历史DAO
 */
@Dao
interface GameHistoryDao {
    
    @Query("SELECT * FROM game_history ORDER BY startTime DESC")
    suspend fun getAllHistory(): List<GameHistoryEntity>
    
    @Query("SELECT * FROM game_history ORDER BY startTime DESC LIMIT :limit")
    suspend fun getRecentHistory(limit: Int): List<GameHistoryEntity>
    
    @Query("SELECT * FROM game_history WHERE gameId = :gameId")
    suspend fun getGameById(gameId: String): GameHistoryEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGame(game: GameHistoryEntity)
    
    @Delete
    suspend fun deleteGame(game: GameHistoryEntity)
    
    @Query("DELETE FROM game_history")
    suspend fun clearAllHistory()
    
    @Query("SELECT COUNT(*) FROM game_history")
    suspend fun getGameCount(): Int
    
    @Query("SELECT COUNT(*) FROM game_history WHERE result = 'LANDLORD_WIN' AND winnerName = :playerName")
    suspend fun getLandlordWins(playerName: String): Int
    
    @Query("SELECT COUNT(*) FROM game_history WHERE result = 'FARMERS_WIN' AND winnerName = :playerName")
    suspend fun getFarmerWins(playerName: String): Int
    
    @Query("SELECT AVG(duration) FROM game_history")
    suspend fun getAverageGameDuration(): Double?
    
    @Query("SELECT * FROM game_history WHERE winnerName = :playerName ORDER BY startTime DESC")
    suspend fun getPlayerWins(playerName: String): List<GameHistoryEntity>
}

/**
 * 游戏设置DAO
 */
@Dao
interface GameSettingsDao {
    
    @Query("SELECT * FROM game_settings WHERE id = 1")
    suspend fun getSettings(): GameSettingsEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveSettings(settings: GameSettingsEntity)
    
    @Query("UPDATE game_settings SET playerName = :name WHERE id = 1")
    suspend fun updatePlayerName(name: String)
    
    @Query("UPDATE game_settings SET soundEnabled = :enabled WHERE id = 1")
    suspend fun updateSoundEnabled(enabled: Boolean)
    
    @Query("UPDATE game_settings SET musicEnabled = :enabled WHERE id = 1")
    suspend fun updateMusicEnabled(enabled: Boolean)
    
    @Query("UPDATE game_settings SET aiDifficulty1 = :difficulty WHERE id = 1")
    suspend fun updateAIDifficulty1(difficulty: String)
    
    @Query("UPDATE game_settings SET aiDifficulty2 = :difficulty WHERE id = 1")
    suspend fun updateAIDifficulty2(difficulty: String)
    
    @Query("UPDATE game_settings SET theme = :theme WHERE id = 1")
    suspend fun updateTheme(theme: String)
    
    @Query("UPDATE game_settings SET animationEnabled = :enabled WHERE id = 1")
    suspend fun updateAnimationEnabled(enabled: Boolean)
    
    @Query("UPDATE game_settings SET autoPlay = :enabled WHERE id = 1")
    suspend fun updateAutoPlay(enabled: Boolean)
    
    @Query("UPDATE game_settings SET gameSpeed = :speed WHERE id = 1")
    suspend fun updateGameSpeed(speed: Int)
}

/**
 * 类型转换器
 */
class Converters {
    
    @TypeConverter
    fun fromPlayerType(type: PlayerType): String {
        return type.name
    }
    
    @TypeConverter
    fun toPlayerType(type: String): PlayerType {
        return PlayerType.valueOf(type)
    }
    
    @TypeConverter
    fun fromGameResult(result: GameResult): String {
        return result.name
    }
    
    @TypeConverter
    fun toGameResult(result: String): GameResult {
        return GameResult.valueOf(result)
    }
}

/**
 * 游戏数据库
 */
@Database(
    entities = [GameHistoryEntity::class, GameSettingsEntity::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class GameDatabase : RoomDatabase() {
    
    abstract fun gameHistoryDao(): GameHistoryDao
    abstract fun gameSettingsDao(): GameSettingsDao
    
    companion object {
        @Volatile
        private var INSTANCE: GameDatabase? = null
        
        fun getDatabase(context: android.content.Context): GameDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    GameDatabase::class.java,
                    "game_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
