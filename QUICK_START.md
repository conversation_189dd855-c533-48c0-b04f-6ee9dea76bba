# 斗地主游戏 - 快速开始指南

## 🚀 最简单的运行方式

### 环境要求
- **Android Studio**: Electric Eel (2022.1.1) 或更高版本
- **JDK**: 8 或更高版本
- **Android设备/模拟器**: API 24+ (Android 7.0)

### 快速运行步骤

1. **打开项目**
   - 启动Android Studio
   - 选择 "Open an existing project"
   - 选择项目根目录 `MyApplication2`

2. **等待同步**
   - Android Studio会自动开始Gradle同步
   - 首次同步可能需要几分钟下载依赖

3. **运行应用**
   - 连接Android设备或启动模拟器
   - 点击绿色的"Run"按钮 ▶️

## 🔧 如果遇到构建问题

### 常见问题解决

#### 1. Java版本问题
如果看到Java版本错误：
- File → Project Structure → SDK Location
- 确保使用JDK 8或更高版本

#### 2. Gradle同步失败
```bash
# 清理项目
./gradlew clean

# 重新构建
./gradlew assembleDebug
```

#### 3. 依赖下载失败
- 检查网络连接
- 在Android Studio中：File → Invalidate Caches and Restart

### 当前配置版本
项目已配置为兼容较旧环境：
- **AGP**: 7.2.2 (兼容JDK 8)
- **Kotlin**: 1.7.10
- **Gradle**: 7.3.3
- **Compose**: 1.2.0
- **compileSdk**: 33
- **targetSdk**: 33

## 🎮 游戏功能预览

运行成功后，您可以体验：

### 主要功能
- ✅ **完整的斗地主游戏**：三人对战，地主vs农民
- ✅ **智能AI对手**：三档难度可选
- ✅ **现代化UI**：使用Jetpack Compose构建
- ✅ **流畅动画**：卡牌翻转、出牌效果
- ✅ **游戏统计**：历史记录、胜率统计

### 游戏流程
1. **欢迎界面** → 点击"开始游戏"
2. **叫地主阶段** → 选择叫分或不叫
3. **出牌阶段** → 选择手牌出牌或过牌
4. **结算界面** → 查看胜负结果和得分

### 操作说明
- **选择手牌**：点击卡牌选中/取消选中
- **出牌**：选中牌后点击"出牌"按钮
- **过牌**：点击"过牌"按钮
- **清空选择**：点击"清空"按钮

## 📱 支持的设备

### 最低要求
- **Android版本**: 7.0 (API 24)
- **内存**: 2GB RAM
- **存储**: 100MB可用空间

### 推荐配置
- **Android版本**: 8.0+ (API 26+)
- **内存**: 4GB+ RAM
- **屏幕**: 5.0英寸以上

## 🎯 项目特色

### 技术亮点
- **MVVM架构**：清晰的代码结构
- **Jetpack Compose**：现代化UI框架
- **Room数据库**：本地数据存储
- **智能AI**：三档难度算法
- **响应式设计**：适配不同屏幕

### 游戏特色
- **完整规则**：标准斗地主规则
- **智能提示**：自动识别牌型
- **流畅体验**：60fps动画效果
- **数据统计**：详细的游戏记录

## 🐛 故障排除

### 如果应用崩溃
1. 检查设备Android版本是否≥7.0
2. 确保有足够的内存空间
3. 重启应用或设备

### 如果游戏卡顿
1. 关闭其他应用释放内存
2. 在设置中关闭动画效果
3. 使用性能更好的设备

### 如果AI不响应
1. 等待几秒，AI正在"思考"
2. 重新开始游戏
3. 检查游戏状态是否正确

## 📞 获取帮助

如果遇到其他问题：
1. 查看Android Studio的Build窗口错误信息
2. 检查设备日志（Logcat）
3. 尝试在不同设备上运行

## 🎉 享受游戏！

项目包含了完整的斗地主游戏体验，从基础的牌型识别到高级的AI策略，都经过精心设计和测试。

**祝您游戏愉快，好运连连！** 🃏🎮

---

### 快速命令参考

```bash
# 清理项目
./gradlew clean

# 构建Debug版本
./gradlew assembleDebug

# 运行测试
./gradlew test

# 查看项目信息
./gradlew projects
```
