package com.example.myapplication.data.models;

/**
 * 扑克牌点数枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0015\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015j\u0002\b\u0016j\u0002\b\u0017j\u0002\b\u0018j\u0002\b\u0019\u00a8\u0006\u001a"}, d2 = {"Lcom/example/myapplication/data/models/Rank;", "", "value", "", "symbol", "", "(Ljava/lang/String;IILjava/lang/String;)V", "getSymbol", "()Ljava/lang/String;", "getValue", "()I", "THREE", "FOUR", "FIVE", "SIX", "SEVEN", "EIGHT", "NINE", "TEN", "JACK", "QUEEN", "KING", "ACE", "TWO", "SMALL_JOKER", "BIG_JOKER", "app_debug"})
public enum Rank {
    /*public static final*/ THREE /* = new THREE(0, null) */,
    /*public static final*/ FOUR /* = new FOUR(0, null) */,
    /*public static final*/ FIVE /* = new FIVE(0, null) */,
    /*public static final*/ SIX /* = new SIX(0, null) */,
    /*public static final*/ SEVEN /* = new SEVEN(0, null) */,
    /*public static final*/ EIGHT /* = new EIGHT(0, null) */,
    /*public static final*/ NINE /* = new NINE(0, null) */,
    /*public static final*/ TEN /* = new TEN(0, null) */,
    /*public static final*/ JACK /* = new JACK(0, null) */,
    /*public static final*/ QUEEN /* = new QUEEN(0, null) */,
    /*public static final*/ KING /* = new KING(0, null) */,
    /*public static final*/ ACE /* = new ACE(0, null) */,
    /*public static final*/ TWO /* = new TWO(0, null) */,
    /*public static final*/ SMALL_JOKER /* = new SMALL_JOKER(0, null) */,
    /*public static final*/ BIG_JOKER /* = new BIG_JOKER(0, null) */;
    private final int value = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String symbol = null;
    
    Rank(int value, java.lang.String symbol) {
    }
    
    public final int getValue() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSymbol() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.myapplication.data.models.Rank> getEntries() {
        return null;
    }
}