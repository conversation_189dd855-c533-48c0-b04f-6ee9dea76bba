package com.example.myapplication.data.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 扑克牌花色枚举
 */
enum class Suit(val value: Int, val symbol: String, val color: CardColor) {
    SPADES(1, "♠", CardColor.BLACK),    // 黑桃
    HEARTS(2, "♥", CardColor.RED),     // 红桃
    CLUBS(3, "♣", CardColor.BLACK),    // 梅花
    DIAMONDS(4, "♦", CardColor.RED),   // 方块
    JOKER(5, "", CardColor.RED)        // 王牌（用于大小王）
}

/**
 * 扑克牌颜色
 */
enum class CardColor {
    RED, BLACK
}

/**
 * 扑克牌点数枚举
 */
enum class Rank(val value: Int, val symbol: String) {
    THREE(3, "3"),
    FOUR(4, "4"),
    FIVE(5, "5"),
    <PERSON><PERSON>(6, "6"),
    SEVEN(7, "7"),
    <PERSON><PERSON><PERSON>(8, "8"),
    <PERSON>IN<PERSON>(9, "9"),
    <PERSON><PERSON>(10, "10"),
    <PERSON><PERSON><PERSON>(11, "J"),
    <PERSON><PERSON><PERSON>(12, "Q"),
    <PERSON><PERSON>(13, "K"),
    <PERSON><PERSON>(14, "A"),
    TWO(15, "2"),
    SMALL_JOKER(16, "小王"),
    BIG_JOKER(17, "大王")
}

/**
 * 扑克牌数据类
 * @param suit 花色
 * @param rank 点数
 */
@Parcelize
data class Card(
    val suit: Suit,
    val rank: Rank
) : Parcelable, Comparable<Card> {
    
    /**
     * 获取牌的显示名称
     */
    val displayName: String
        get() = when {
            rank == Rank.SMALL_JOKER -> "小王"
            rank == Rank.BIG_JOKER -> "大王"
            else -> "${suit.symbol}${rank.symbol}"
        }
    
    /**
     * 获取牌的权重值（用于比较大小）
     */
    val weight: Int
        get() = when {
            rank == Rank.BIG_JOKER -> 17
            rank == Rank.SMALL_JOKER -> 16
            rank == Rank.TWO -> 15
            rank == Rank.ACE -> 14
            else -> rank.value
        }
    
    /**
     * 是否为王牌
     */
    val isJoker: Boolean
        get() = rank == Rank.SMALL_JOKER || rank == Rank.BIG_JOKER
    
    /**
     * 是否为大王
     */
    val isBigJoker: Boolean
        get() = rank == Rank.BIG_JOKER
    
    /**
     * 是否为小王
     */
    val isSmallJoker: Boolean
        get() = rank == Rank.SMALL_JOKER
    
    /**
     * 比较牌的大小
     */
    override fun compareTo(other: Card): Int {
        return this.weight.compareTo(other.weight)
    }
    
    companion object {
        /**
         * 创建一副完整的54张牌
         */
        fun createFullDeck(): List<Card> {
            val cards = mutableListOf<Card>()
            
            // 添加52张普通牌
            for (suit in listOf(Suit.SPADES, Suit.HEARTS, Suit.CLUBS, Suit.DIAMONDS)) {
                for (rank in listOf(
                    Rank.THREE, Rank.FOUR, Rank.FIVE, Rank.SIX, Rank.SEVEN,
                    Rank.EIGHT, Rank.NINE, Rank.TEN, Rank.JACK, Rank.QUEEN,
                    Rank.KING, Rank.ACE, Rank.TWO
                )) {
                    cards.add(Card(suit, rank))
                }
            }
            
            // 添加大小王
            cards.add(Card(Suit.JOKER, Rank.SMALL_JOKER))
            cards.add(Card(Suit.JOKER, Rank.BIG_JOKER))
            
            return cards
        }
        
        /**
         * 根据字符串创建牌（用于测试）
         */
        fun fromString(cardStr: String): Card? {
            return when (cardStr) {
                "小王" -> Card(Suit.JOKER, Rank.SMALL_JOKER)
                "大王" -> Card(Suit.JOKER, Rank.BIG_JOKER)
                else -> {
                    if (cardStr.length < 2) return null
                    val suitChar = cardStr[0]
                    val rankStr = cardStr.substring(1)
                    
                    val suit = when (suitChar) {
                        '♠' -> Suit.SPADES
                        '♥' -> Suit.HEARTS
                        '♣' -> Suit.CLUBS
                        '♦' -> Suit.DIAMONDS
                        else -> return null
                    }
                    
                    val rank = Rank.values().find { it.symbol == rankStr } ?: return null
                    Card(suit, rank)
                }
            }
        }
    }
}
