package com.example.myapplication.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class GameHistoryDao_Impl implements GameHistoryDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<GameHistoryEntity> __insertionAdapterOfGameHistoryEntity;

  private final EntityDeletionOrUpdateAdapter<GameHistoryEntity> __deletionAdapterOfGameHistoryEntity;

  private final SharedSQLiteStatement __preparedStmtOfClearAllHistory;

  public GameHistoryDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfGameHistoryEntity = new EntityInsertionAdapter<GameHistoryEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `game_history` (`gameId`,`startTime`,`endTime`,`duration`,`result`,`winnerName`,`playerNames`,`playerScores`,`bombCount`,`isSpring`,`landlordIndex`,`maxCallingScore`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GameHistoryEntity entity) {
        if (entity.getGameId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getGameId());
        }
        statement.bindLong(2, entity.getStartTime());
        statement.bindLong(3, entity.getEndTime());
        statement.bindLong(4, entity.getDuration());
        if (entity.getResult() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getResult());
        }
        if (entity.getWinnerName() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getWinnerName());
        }
        if (entity.getPlayerNames() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getPlayerNames());
        }
        if (entity.getPlayerScores() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getPlayerScores());
        }
        statement.bindLong(9, entity.getBombCount());
        final int _tmp = entity.isSpring() ? 1 : 0;
        statement.bindLong(10, _tmp);
        statement.bindLong(11, entity.getLandlordIndex());
        statement.bindLong(12, entity.getMaxCallingScore());
      }
    };
    this.__deletionAdapterOfGameHistoryEntity = new EntityDeletionOrUpdateAdapter<GameHistoryEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `game_history` WHERE `gameId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GameHistoryEntity entity) {
        if (entity.getGameId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getGameId());
        }
      }
    };
    this.__preparedStmtOfClearAllHistory = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM game_history";
        return _query;
      }
    };
  }

  @Override
  public Object insertGame(final GameHistoryEntity game,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfGameHistoryEntity.insert(game);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteGame(final GameHistoryEntity game,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfGameHistoryEntity.handle(game);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAllHistory(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAllHistory.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAllHistory.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllHistory(final Continuation<? super List<GameHistoryEntity>> $completion) {
    final String _sql = "SELECT * FROM game_history ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GameHistoryEntity>>() {
      @Override
      @NonNull
      public List<GameHistoryEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfGameId = CursorUtil.getColumnIndexOrThrow(_cursor, "gameId");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfResult = CursorUtil.getColumnIndexOrThrow(_cursor, "result");
          final int _cursorIndexOfWinnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "winnerName");
          final int _cursorIndexOfPlayerNames = CursorUtil.getColumnIndexOrThrow(_cursor, "playerNames");
          final int _cursorIndexOfPlayerScores = CursorUtil.getColumnIndexOrThrow(_cursor, "playerScores");
          final int _cursorIndexOfBombCount = CursorUtil.getColumnIndexOrThrow(_cursor, "bombCount");
          final int _cursorIndexOfIsSpring = CursorUtil.getColumnIndexOrThrow(_cursor, "isSpring");
          final int _cursorIndexOfLandlordIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "landlordIndex");
          final int _cursorIndexOfMaxCallingScore = CursorUtil.getColumnIndexOrThrow(_cursor, "maxCallingScore");
          final List<GameHistoryEntity> _result = new ArrayList<GameHistoryEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GameHistoryEntity _item;
            final String _tmpGameId;
            if (_cursor.isNull(_cursorIndexOfGameId)) {
              _tmpGameId = null;
            } else {
              _tmpGameId = _cursor.getString(_cursorIndexOfGameId);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpResult;
            if (_cursor.isNull(_cursorIndexOfResult)) {
              _tmpResult = null;
            } else {
              _tmpResult = _cursor.getString(_cursorIndexOfResult);
            }
            final String _tmpWinnerName;
            if (_cursor.isNull(_cursorIndexOfWinnerName)) {
              _tmpWinnerName = null;
            } else {
              _tmpWinnerName = _cursor.getString(_cursorIndexOfWinnerName);
            }
            final String _tmpPlayerNames;
            if (_cursor.isNull(_cursorIndexOfPlayerNames)) {
              _tmpPlayerNames = null;
            } else {
              _tmpPlayerNames = _cursor.getString(_cursorIndexOfPlayerNames);
            }
            final String _tmpPlayerScores;
            if (_cursor.isNull(_cursorIndexOfPlayerScores)) {
              _tmpPlayerScores = null;
            } else {
              _tmpPlayerScores = _cursor.getString(_cursorIndexOfPlayerScores);
            }
            final int _tmpBombCount;
            _tmpBombCount = _cursor.getInt(_cursorIndexOfBombCount);
            final boolean _tmpIsSpring;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSpring);
            _tmpIsSpring = _tmp != 0;
            final int _tmpLandlordIndex;
            _tmpLandlordIndex = _cursor.getInt(_cursorIndexOfLandlordIndex);
            final int _tmpMaxCallingScore;
            _tmpMaxCallingScore = _cursor.getInt(_cursorIndexOfMaxCallingScore);
            _item = new GameHistoryEntity(_tmpGameId,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpResult,_tmpWinnerName,_tmpPlayerNames,_tmpPlayerScores,_tmpBombCount,_tmpIsSpring,_tmpLandlordIndex,_tmpMaxCallingScore);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecentHistory(final int limit,
      final Continuation<? super List<GameHistoryEntity>> $completion) {
    final String _sql = "SELECT * FROM game_history ORDER BY startTime DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GameHistoryEntity>>() {
      @Override
      @NonNull
      public List<GameHistoryEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfGameId = CursorUtil.getColumnIndexOrThrow(_cursor, "gameId");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfResult = CursorUtil.getColumnIndexOrThrow(_cursor, "result");
          final int _cursorIndexOfWinnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "winnerName");
          final int _cursorIndexOfPlayerNames = CursorUtil.getColumnIndexOrThrow(_cursor, "playerNames");
          final int _cursorIndexOfPlayerScores = CursorUtil.getColumnIndexOrThrow(_cursor, "playerScores");
          final int _cursorIndexOfBombCount = CursorUtil.getColumnIndexOrThrow(_cursor, "bombCount");
          final int _cursorIndexOfIsSpring = CursorUtil.getColumnIndexOrThrow(_cursor, "isSpring");
          final int _cursorIndexOfLandlordIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "landlordIndex");
          final int _cursorIndexOfMaxCallingScore = CursorUtil.getColumnIndexOrThrow(_cursor, "maxCallingScore");
          final List<GameHistoryEntity> _result = new ArrayList<GameHistoryEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GameHistoryEntity _item;
            final String _tmpGameId;
            if (_cursor.isNull(_cursorIndexOfGameId)) {
              _tmpGameId = null;
            } else {
              _tmpGameId = _cursor.getString(_cursorIndexOfGameId);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpResult;
            if (_cursor.isNull(_cursorIndexOfResult)) {
              _tmpResult = null;
            } else {
              _tmpResult = _cursor.getString(_cursorIndexOfResult);
            }
            final String _tmpWinnerName;
            if (_cursor.isNull(_cursorIndexOfWinnerName)) {
              _tmpWinnerName = null;
            } else {
              _tmpWinnerName = _cursor.getString(_cursorIndexOfWinnerName);
            }
            final String _tmpPlayerNames;
            if (_cursor.isNull(_cursorIndexOfPlayerNames)) {
              _tmpPlayerNames = null;
            } else {
              _tmpPlayerNames = _cursor.getString(_cursorIndexOfPlayerNames);
            }
            final String _tmpPlayerScores;
            if (_cursor.isNull(_cursorIndexOfPlayerScores)) {
              _tmpPlayerScores = null;
            } else {
              _tmpPlayerScores = _cursor.getString(_cursorIndexOfPlayerScores);
            }
            final int _tmpBombCount;
            _tmpBombCount = _cursor.getInt(_cursorIndexOfBombCount);
            final boolean _tmpIsSpring;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSpring);
            _tmpIsSpring = _tmp != 0;
            final int _tmpLandlordIndex;
            _tmpLandlordIndex = _cursor.getInt(_cursorIndexOfLandlordIndex);
            final int _tmpMaxCallingScore;
            _tmpMaxCallingScore = _cursor.getInt(_cursorIndexOfMaxCallingScore);
            _item = new GameHistoryEntity(_tmpGameId,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpResult,_tmpWinnerName,_tmpPlayerNames,_tmpPlayerScores,_tmpBombCount,_tmpIsSpring,_tmpLandlordIndex,_tmpMaxCallingScore);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getGameById(final String gameId,
      final Continuation<? super GameHistoryEntity> $completion) {
    final String _sql = "SELECT * FROM game_history WHERE gameId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (gameId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, gameId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<GameHistoryEntity>() {
      @Override
      @Nullable
      public GameHistoryEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfGameId = CursorUtil.getColumnIndexOrThrow(_cursor, "gameId");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfResult = CursorUtil.getColumnIndexOrThrow(_cursor, "result");
          final int _cursorIndexOfWinnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "winnerName");
          final int _cursorIndexOfPlayerNames = CursorUtil.getColumnIndexOrThrow(_cursor, "playerNames");
          final int _cursorIndexOfPlayerScores = CursorUtil.getColumnIndexOrThrow(_cursor, "playerScores");
          final int _cursorIndexOfBombCount = CursorUtil.getColumnIndexOrThrow(_cursor, "bombCount");
          final int _cursorIndexOfIsSpring = CursorUtil.getColumnIndexOrThrow(_cursor, "isSpring");
          final int _cursorIndexOfLandlordIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "landlordIndex");
          final int _cursorIndexOfMaxCallingScore = CursorUtil.getColumnIndexOrThrow(_cursor, "maxCallingScore");
          final GameHistoryEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpGameId;
            if (_cursor.isNull(_cursorIndexOfGameId)) {
              _tmpGameId = null;
            } else {
              _tmpGameId = _cursor.getString(_cursorIndexOfGameId);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpResult;
            if (_cursor.isNull(_cursorIndexOfResult)) {
              _tmpResult = null;
            } else {
              _tmpResult = _cursor.getString(_cursorIndexOfResult);
            }
            final String _tmpWinnerName;
            if (_cursor.isNull(_cursorIndexOfWinnerName)) {
              _tmpWinnerName = null;
            } else {
              _tmpWinnerName = _cursor.getString(_cursorIndexOfWinnerName);
            }
            final String _tmpPlayerNames;
            if (_cursor.isNull(_cursorIndexOfPlayerNames)) {
              _tmpPlayerNames = null;
            } else {
              _tmpPlayerNames = _cursor.getString(_cursorIndexOfPlayerNames);
            }
            final String _tmpPlayerScores;
            if (_cursor.isNull(_cursorIndexOfPlayerScores)) {
              _tmpPlayerScores = null;
            } else {
              _tmpPlayerScores = _cursor.getString(_cursorIndexOfPlayerScores);
            }
            final int _tmpBombCount;
            _tmpBombCount = _cursor.getInt(_cursorIndexOfBombCount);
            final boolean _tmpIsSpring;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSpring);
            _tmpIsSpring = _tmp != 0;
            final int _tmpLandlordIndex;
            _tmpLandlordIndex = _cursor.getInt(_cursorIndexOfLandlordIndex);
            final int _tmpMaxCallingScore;
            _tmpMaxCallingScore = _cursor.getInt(_cursorIndexOfMaxCallingScore);
            _result = new GameHistoryEntity(_tmpGameId,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpResult,_tmpWinnerName,_tmpPlayerNames,_tmpPlayerScores,_tmpBombCount,_tmpIsSpring,_tmpLandlordIndex,_tmpMaxCallingScore);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getGameCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM game_history";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLandlordWins(final String playerName,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM game_history WHERE result = 'LANDLORD_WIN' AND winnerName = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (playerName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, playerName);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getFarmerWins(final String playerName,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM game_history WHERE result = 'FARMERS_WIN' AND winnerName = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (playerName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, playerName);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageGameDuration(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(duration) FROM game_history";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPlayerWins(final String playerName,
      final Continuation<? super List<GameHistoryEntity>> $completion) {
    final String _sql = "SELECT * FROM game_history WHERE winnerName = ? ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (playerName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, playerName);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GameHistoryEntity>>() {
      @Override
      @NonNull
      public List<GameHistoryEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfGameId = CursorUtil.getColumnIndexOrThrow(_cursor, "gameId");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfResult = CursorUtil.getColumnIndexOrThrow(_cursor, "result");
          final int _cursorIndexOfWinnerName = CursorUtil.getColumnIndexOrThrow(_cursor, "winnerName");
          final int _cursorIndexOfPlayerNames = CursorUtil.getColumnIndexOrThrow(_cursor, "playerNames");
          final int _cursorIndexOfPlayerScores = CursorUtil.getColumnIndexOrThrow(_cursor, "playerScores");
          final int _cursorIndexOfBombCount = CursorUtil.getColumnIndexOrThrow(_cursor, "bombCount");
          final int _cursorIndexOfIsSpring = CursorUtil.getColumnIndexOrThrow(_cursor, "isSpring");
          final int _cursorIndexOfLandlordIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "landlordIndex");
          final int _cursorIndexOfMaxCallingScore = CursorUtil.getColumnIndexOrThrow(_cursor, "maxCallingScore");
          final List<GameHistoryEntity> _result = new ArrayList<GameHistoryEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GameHistoryEntity _item;
            final String _tmpGameId;
            if (_cursor.isNull(_cursorIndexOfGameId)) {
              _tmpGameId = null;
            } else {
              _tmpGameId = _cursor.getString(_cursorIndexOfGameId);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpResult;
            if (_cursor.isNull(_cursorIndexOfResult)) {
              _tmpResult = null;
            } else {
              _tmpResult = _cursor.getString(_cursorIndexOfResult);
            }
            final String _tmpWinnerName;
            if (_cursor.isNull(_cursorIndexOfWinnerName)) {
              _tmpWinnerName = null;
            } else {
              _tmpWinnerName = _cursor.getString(_cursorIndexOfWinnerName);
            }
            final String _tmpPlayerNames;
            if (_cursor.isNull(_cursorIndexOfPlayerNames)) {
              _tmpPlayerNames = null;
            } else {
              _tmpPlayerNames = _cursor.getString(_cursorIndexOfPlayerNames);
            }
            final String _tmpPlayerScores;
            if (_cursor.isNull(_cursorIndexOfPlayerScores)) {
              _tmpPlayerScores = null;
            } else {
              _tmpPlayerScores = _cursor.getString(_cursorIndexOfPlayerScores);
            }
            final int _tmpBombCount;
            _tmpBombCount = _cursor.getInt(_cursorIndexOfBombCount);
            final boolean _tmpIsSpring;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSpring);
            _tmpIsSpring = _tmp != 0;
            final int _tmpLandlordIndex;
            _tmpLandlordIndex = _cursor.getInt(_cursorIndexOfLandlordIndex);
            final int _tmpMaxCallingScore;
            _tmpMaxCallingScore = _cursor.getInt(_cursorIndexOfMaxCallingScore);
            _item = new GameHistoryEntity(_tmpGameId,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpResult,_tmpWinnerName,_tmpPlayerNames,_tmpPlayerScores,_tmpBombCount,_tmpIsSpring,_tmpLandlordIndex,_tmpMaxCallingScore);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
