package com.example.myapplication.domain

import com.example.myapplication.data.models.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 游戏状态管理器
 * 负责管理游戏的各个阶段和状态转换
 */
class GameStateManager {
    
    // 游戏状态
    private val _gameState = MutableStateFlow(GameState.newGame())
    val gameState: StateFlow<GameState> = _gameState.asStateFlow()
    
    // 叫地主分数记录
    private val _callingScores = MutableStateFlow<Map<Int, Int>>(emptyMap())
    val callingScores: StateFlow<Map<Int, Int>> = _callingScores.asStateFlow()
    
    // 出牌历史
    private val _playHistory = MutableStateFlow<List<PlayRecord>>(emptyList())
    val playHistory: StateFlow<List<PlayRecord>> = _playHistory.asStateFlow()
    
    /**
     * 出牌记录数据类
     */
    data class PlayRecord(
        val playerIndex: Int,
        val playerName: String,
        val cards: List<Card>,
        val pattern: CardPattern,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * 开始新游戏
     */
    fun startNewGame() {
        _gameState.value = GameState.newGame()
        _callingScores.value = emptyMap()
        _playHistory.value = emptyList()
    }
    
    /**
     * 进入发牌阶段
     */
    fun enterDealingPhase() {
        updateGameState { it.copy(phase = GamePhase.DEALING) }
    }
    
    /**
     * 进入叫地主阶段
     */
    fun enterCallingPhase(startPlayerIndex: Int = 0) {
        updateGameState { 
            it.copy(
                phase = GamePhase.CALLING,
                currentPlayerIndex = startPlayerIndex,
                callingRound = 0
            )
        }
    }
    
    /**
     * 进入出牌阶段
     */
    fun enterPlayingPhase(landlordIndex: Int) {
        updateGameState { 
            it.copy(
                phase = GamePhase.PLAYING,
                landlordIndex = landlordIndex,
                currentPlayerIndex = landlordIndex
            )
        }
    }
    
    /**
     * 结束游戏
     */
    fun endGame(winnerIndex: Int, result: GameResult) {
        updateGameState { 
            it.copy(
                phase = GamePhase.FINISHED,
                winnerIndex = winnerIndex,
                result = result,
                gameEndTime = System.currentTimeMillis()
            )
        }
    }
    
    /**
     * 玩家叫地主
     */
    fun playerCallLandlord(playerIndex: Int, score: Int): Boolean {
        val currentState = _gameState.value
        
        if (currentState.phase != GamePhase.CALLING) return false
        if (currentState.currentPlayerIndex != playerIndex) return false
        
        // 记录叫地主分数
        val newCallingScores = _callingScores.value.toMutableMap()
        newCallingScores[playerIndex] = score
        _callingScores.value = newCallingScores
        
        // 更新最高叫分
        if (score > currentState.maxCallingScore) {
            updateGameState { 
                it.copy(
                    maxCallingScore = score,
                    callingPlayerIndex = playerIndex
                )
            }
        }
        
        // 增加叫地主轮数
        updateGameState { it.copy(callingRound = it.callingRound + 1) }
        
        return true
    }
    
    /**
     * 切换到下一个玩家
     */
    fun nextPlayer(playerCount: Int = 3) {
        updateGameState { 
            it.copy(currentPlayerIndex = (it.currentPlayerIndex + 1) % playerCount)
        }
    }
    
    /**
     * 设置地主
     */
    fun setLandlord(playerIndex: Int, landlordCards: List<Card>) {
        updateGameState { 
            it.copy(
                landlordIndex = playerIndex,
                landlordCards = landlordCards,
                phase = GamePhase.PLAYING,
                currentPlayerIndex = playerIndex
            )
        }
    }
    
    /**
     * 玩家出牌
     */
    fun playerPlayCards(
        playerIndex: Int,
        playerName: String,
        cards: List<Card>,
        pattern: CardPattern
    ) {
        // 记录出牌历史
        val playRecord = PlayRecord(playerIndex, playerName, cards, pattern)
        _playHistory.value = _playHistory.value + playRecord
        
        // 更新游戏状态
        updateGameState { 
            it.copy(
                lastPlayedCards = cards,
                lastPlayedPattern = pattern,
                lastPlayerIndex = playerIndex,
                passCount = 0
            )
        }
        
        // 如果是炸弹，增加炸弹计数
        if (pattern.isBomb) {
            updateGameState { it.copy(bombCount = it.bombCount + 1) }
        }
    }
    
    /**
     * 玩家过牌
     */
    fun playerPass(playerIndex: Int, playerName: String) {
        // 记录过牌历史
        val playRecord = PlayRecord(
            playerIndex = playerIndex,
            playerName = playerName,
            cards = emptyList(),
            pattern = CardPattern.invalid()
        )
        _playHistory.value = _playHistory.value + playRecord
        
        // 更新过牌计数
        val newPassCount = _gameState.value.passCount + 1
        updateGameState { it.copy(passCount = newPassCount) }
        
        // 如果连续两个玩家过牌，清空上次出牌记录
        if (newPassCount >= 2) {
            clearLastPlay()
        }
    }
    
    /**
     * 清空上次出牌记录
     */
    fun clearLastPlay() {
        updateGameState { 
            it.copy(
                lastPlayedCards = emptyList(),
                lastPlayedPattern = null,
                lastPlayerIndex = -1,
                passCount = 0
            )
        }
    }
    
    /**
     * 检查是否春天
     */
    fun checkSpring(winnerRole: PlayerRole, playHistory: List<PlayRecord>): Boolean {
        return when (winnerRole) {
            PlayerRole.LANDLORD -> {
                // 地主春天：农民一张牌都没出过
                val farmerPlays = playHistory.filter { record ->
                    record.cards.isNotEmpty() && 
                    record.playerIndex != _gameState.value.landlordIndex
                }
                farmerPlays.isEmpty()
            }
            PlayerRole.FARMER -> {
                // 农民春天：地主只出过一手牌
                val landlordPlays = playHistory.filter { record ->
                    record.cards.isNotEmpty() && 
                    record.playerIndex == _gameState.value.landlordIndex
                }
                landlordPlays.size <= 1
            }
        }
    }
    
    /**
     * 获取当前游戏阶段
     */
    fun getCurrentPhase(): GamePhase {
        return _gameState.value.phase
    }
    
    /**
     * 获取当前玩家索引
     */
    fun getCurrentPlayerIndex(): Int {
        return _gameState.value.currentPlayerIndex
    }
    
    /**
     * 获取地主索引
     */
    fun getLandlordIndex(): Int {
        return _gameState.value.landlordIndex
    }
    
    /**
     * 获取最后出牌信息
     */
    fun getLastPlayInfo(): Triple<List<Card>, CardPattern?, Int> {
        val state = _gameState.value
        return Triple(state.lastPlayedCards, state.lastPlayedPattern, state.lastPlayerIndex)
    }
    
    /**
     * 获取游戏统计信息
     */
    fun getGameStats(): Map<String, Any> {
        val state = _gameState.value
        return mapOf(
            "gameId" to state.gameId,
            "phase" to state.phase.name,
            "duration" to (System.currentTimeMillis() - state.gameStartTime),
            "bombCount" to state.bombCount,
            "isSpring" to state.isSpring,
            "playCount" to _playHistory.value.size,
            "callingScores" to _callingScores.value
        )
    }
    
    /**
     * 重置游戏状态
     */
    fun resetGame() {
        _gameState.value.reset()
        _callingScores.value = emptyMap()
        _playHistory.value = emptyList()
    }
    
    /**
     * 是否可以出牌
     */
    fun canPlayCards(playerIndex: Int): Boolean {
        val state = _gameState.value
        return state.phase == GamePhase.PLAYING && 
               state.currentPlayerIndex == playerIndex
    }
    
    /**
     * 是否可以叫地主
     */
    fun canCallLandlord(playerIndex: Int): Boolean {
        val state = _gameState.value
        return state.phase == GamePhase.CALLING && 
               state.currentPlayerIndex == playerIndex &&
               !_callingScores.value.containsKey(playerIndex)
    }
    
    /**
     * 获取最高叫地主分数
     */
    fun getMaxCallingScore(): Int {
        return _callingScores.value.values.maxOrNull() ?: 0
    }
    
    /**
     * 获取叫地主最高分的玩家
     */
    fun getHighestCallingPlayer(): Int? {
        return _callingScores.value.maxByOrNull { it.value }?.key
    }
    
    /**
     * 更新游戏状态的辅助方法
     */
    private fun updateGameState(update: (GameState) -> GameState) {
        _gameState.value = update(_gameState.value)
    }
}
