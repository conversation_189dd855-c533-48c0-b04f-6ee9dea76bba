package com.example.myapplication.data.models

/**
 * 牌型枚举
 */
enum class CardPatternType(val displayName: String, val priority: Int) {
    INVALID("无效牌型", 0),
    SINGLE("单牌", 1),
    PAIR("对子", 2),
    TRIPLE("三张", 3),
    TRIPLE_WITH_SINGLE("三带一", 4),
    TRIPLE_WITH_PAIR("三带二", 5),
    STRAIGHT("顺子", 6),
    PAIR_STRAIGHT("连对", 7),
    TRIPLE_STRAIGHT("飞机", 8),
    TRIPLE_STRAIGHT_WITH_SINGLES("飞机带单牌", 9),
    TRIPLE_STRAIGHT_WITH_PAIRS("飞机带对子", 10),
    FOUR_WITH_TWO_SINGLES("四带二单", 11),
    FOUR_WITH_TWO_PAIRS("四带二对", 12),
    BOMB("炸弹", 13),
    ROCKET("王炸", 14)
}

/**
 * 牌型数据类
 */
data class CardPattern(
    val type: CardPatternType,
    val cards: List<Card>,
    val mainRank: Rank? = null,  // 主要牌的点数（用于比较大小）
    val length: Int = 0          // 连牌的长度
) {
    
    /**
     * 牌型的权重值（用于比较大小）
     */
    val weight: Int
        get() = when (type) {
            CardPatternType.ROCKET -> Int.MAX_VALUE
            CardPatternType.BOMB -> 1000000 + (mainRank?.value ?: 0)
            else -> (mainRank?.value ?: 0) * 1000 + type.priority
        }
    
    /**
     * 是否为有效牌型
     */
    val isValid: Boolean
        get() = type != CardPatternType.INVALID
    
    /**
     * 是否为炸弹类型（包括王炸）
     */
    val isBomb: Boolean
        get() = type == CardPatternType.BOMB || type == CardPatternType.ROCKET
    
    /**
     * 是否可以压过指定牌型
     */
    fun canBeat(other: CardPattern?): Boolean {
        if (other == null) return true
        if (!isValid || !other.isValid) return false
        
        return when {
            // 王炸可以压过所有牌
            type == CardPatternType.ROCKET -> true
            // 其他牌型不能压过王炸
            other.type == CardPatternType.ROCKET -> false
            // 炸弹可以压过非炸弹牌型
            type == CardPatternType.BOMB && other.type != CardPatternType.BOMB -> true
            // 非炸弹不能压过炸弹
            type != CardPatternType.BOMB && other.type == CardPatternType.BOMB -> false
            // 同类型牌型比较权重
            type == other.type -> {
                when (type) {
                    CardPatternType.STRAIGHT,
                    CardPatternType.PAIR_STRAIGHT,
                    CardPatternType.TRIPLE_STRAIGHT,
                    CardPatternType.TRIPLE_STRAIGHT_WITH_SINGLES,
                    CardPatternType.TRIPLE_STRAIGHT_WITH_PAIRS -> {
                        // 连牌需要长度相同才能比较
                        length == other.length && weight > other.weight
                    }
                    else -> weight > other.weight
                }
            }
            // 不同类型的非炸弹牌型不能互相压制
            else -> false
        }
    }
    
    companion object {
        /**
         * 创建无效牌型
         */
        fun invalid(): CardPattern {
            return CardPattern(CardPatternType.INVALID, emptyList())
        }
        
        /**
         * 创建单牌
         */
        fun single(card: Card): CardPattern {
            return CardPattern(
                type = CardPatternType.SINGLE,
                cards = listOf(card),
                mainRank = card.rank
            )
        }
        
        /**
         * 创建对子
         */
        fun pair(cards: List<Card>): CardPattern {
            return if (cards.size == 2 && cards[0].rank == cards[1].rank) {
                CardPattern(
                    type = CardPatternType.PAIR,
                    cards = cards,
                    mainRank = cards[0].rank
                )
            } else {
                invalid()
            }
        }
        
        /**
         * 创建三张
         */
        fun triple(cards: List<Card>): CardPattern {
            return if (cards.size == 3 && cards.all { it.rank == cards[0].rank }) {
                CardPattern(
                    type = CardPatternType.TRIPLE,
                    cards = cards,
                    mainRank = cards[0].rank
                )
            } else {
                invalid()
            }
        }
        
        /**
         * 创建炸弹
         */
        fun bomb(cards: List<Card>): CardPattern {
            return if (cards.size == 4 && cards.all { it.rank == cards[0].rank }) {
                CardPattern(
                    type = CardPatternType.BOMB,
                    cards = cards,
                    mainRank = cards[0].rank
                )
            } else {
                invalid()
            }
        }
        
        /**
         * 创建王炸
         */
        fun rocket(cards: List<Card>): CardPattern {
            return if (cards.size == 2 && 
                      cards.any { it.rank == Rank.SMALL_JOKER } &&
                      cards.any { it.rank == Rank.BIG_JOKER }) {
                CardPattern(
                    type = CardPatternType.ROCKET,
                    cards = cards,
                    mainRank = Rank.BIG_JOKER
                )
            } else {
                invalid()
            }
        }
    }
}
