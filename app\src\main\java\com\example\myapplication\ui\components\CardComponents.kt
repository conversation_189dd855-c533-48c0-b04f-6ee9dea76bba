package com.example.myapplication.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.data.models.*

/**
 * 扑克牌组件
 */
@Composable
fun CardComponent(
    card: Card,
    isSelected: Boolean = false,
    isEnabled: Boolean = true,
    size: CardSize = CardSize.NORMAL,
    isHighlighted: Boolean = false,
    showAnimation: Boolean = true,
    onClick: (() -> Unit)? = null
) {
    val cardWidth = when (size) {
        CardSize.SMALL -> 32.dp
        CardSize.NORMAL -> 48.dp
        CardSize.LARGE -> 64.dp
    }
    val cardHeight = when (size) {
        CardSize.SMALL -> 44.dp
        CardSize.NORMAL -> 68.dp
        CardSize.LARGE -> 88.dp
    }
    
    val backgroundColor = when {
        card.isJoker -> if (card.isBigJoker) Color.Red else Color.Black
        card.suit.color == CardColor.RED -> Color.Red
        else -> Color.Black
    }
    
    val cardColor = when {
        isSelected -> MaterialTheme.colorScheme.primary.copy(alpha = 0.9f)
        isHighlighted -> MaterialTheme.colorScheme.secondary.copy(alpha = 0.7f)
        else -> Color.White
    }

    val borderColor = when {
        isSelected -> MaterialTheme.colorScheme.primary
        isHighlighted -> MaterialTheme.colorScheme.secondary
        else -> Color.Gray.copy(alpha = 0.5f)
    }

    val borderWidth = when {
        isSelected -> 3.dp
        isHighlighted -> 2.dp
        else -> 1.dp
    }
    
    Card(
        modifier = Modifier
            .width(cardWidth)
            .height(cardHeight)
            .then(
                if (onClick != null && isEnabled) {
                    Modifier.clickable { onClick() }
                } else {
                    Modifier
                }
            ),
        shape = RoundedCornerShape(6.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        border = BorderStroke(
            width = borderWidth,
            color = borderColor
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 8.dp else 2.dp
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // 花色符号
                if (!card.isJoker) {
                    Text(
                        text = card.suit.symbol,
                        color = backgroundColor,
                        fontSize = when (size) {
                            CardSize.SMALL -> 12.sp
                            CardSize.NORMAL -> 16.sp
                            CardSize.LARGE -> 20.sp
                        },
                        fontWeight = FontWeight.Bold
                    )
                }
                
                // 点数或王牌标识
                Text(
                    text = if (card.isJoker) {
                        when (size) {
                            CardSize.SMALL -> if (card.isBigJoker) "大" else "小"
                            else -> card.rank.symbol
                        }
                    } else {
                        card.rank.symbol
                    },
                    color = backgroundColor,
                    fontSize = when (size) {
                        CardSize.SMALL -> 10.sp
                        CardSize.NORMAL -> 14.sp
                        CardSize.LARGE -> 18.sp
                    },
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 * 卡牌尺寸枚举
 */
enum class CardSize {
    SMALL, NORMAL, LARGE
}

/**
 * 智能提示组件
 */
@Composable
fun SmartHintComponent(
    suggestedCards: List<Card>,
    onAcceptHint: () -> Unit,
    onDismissHint: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (suggestedCards.isNotEmpty()) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(8.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.8f)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "智能提示",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )

                    IconButton(
                        onClick = onDismissHint,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭提示",
                            tint = MaterialTheme.colorScheme.onPrimaryContainer,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 显示建议的牌
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    items(suggestedCards) { card ->
                        CardComponent(
                            card = card,
                            size = CardSize.SMALL,
                            isHighlighted = true,
                            isEnabled = false
                        )
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                Button(
                    onClick = onAcceptHint,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text("采用建议")
                }
            }
        }
    }
}

/**
 * 玩家手牌区域
 */
@Composable
fun PlayerHandArea(
    humanPlayer: Player?,
    selectedCards: Set<Card>,
    onCardClick: (Card) -> Unit,
    canPlay: Boolean
) {
    if (humanPlayer == null) return
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 手牌标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "我的手牌",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = "${humanPlayer.handSize} 张",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 手牌列表
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy((-16).dp),
                contentPadding = PaddingValues(horizontal = 8.dp)
            ) {
                items(humanPlayer.hand) { card ->
                    CardComponent(
                        card = card,
                        isSelected = selectedCards.contains(card),
                        isEnabled = canPlay,
                        size = CardSize.NORMAL,
                        onClick = { onCardClick(card) }
                    )
                }
            }
        }
    }
}

/**
 * 对手区域
 */
@Composable
fun OpponentAreas(
    players: List<Player>,
    gameState: GameState
) {
    val leftPlayer = players.find { it.position == PlayerPosition.LEFT }
    val rightPlayer = players.find { it.position == PlayerPosition.RIGHT }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // 左侧对手
        leftPlayer?.let { player ->
            OpponentCard(
                player = player,
                isCurrentPlayer = gameState.currentPlayerIndex == players.indexOf(player),
                modifier = Modifier.weight(1f)
            )
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // 右侧对手
        rightPlayer?.let { player ->
            OpponentCard(
                player = player,
                isCurrentPlayer = gameState.currentPlayerIndex == players.indexOf(player),
                modifier = Modifier.weight(1f)
            )
        }
    }
}

/**
 * 对手信息卡片
 */
@Composable
fun OpponentCard(
    player: Player,
    isCurrentPlayer: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isCurrentPlayer) {
                MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
            } else {
                MaterialTheme.colorScheme.surface.copy(alpha = 0.8f)
            }
        ),
        border = if (isCurrentPlayer) {
            BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
        } else null
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 玩家名称
            Text(
                text = player.name,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 角色标识
            player.role?.let { role ->
                Text(
                    text = if (role == PlayerRole.LANDLORD) "地主" else "农民",
                    fontSize = 12.sp,
                    color = if (role == PlayerRole.LANDLORD) Color.Red else Color.Blue,
                    fontWeight = FontWeight.Medium
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 手牌数量
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                repeat(minOf(player.handSize, 10)) {
                    Box(
                        modifier = Modifier
                            .size(16.dp, 20.dp)
                            .background(
                                Color.Gray.copy(alpha = 0.6f),
                                RoundedCornerShape(2.dp)
                            )
                            .border(
                                0.5.dp,
                                Color.Gray,
                                RoundedCornerShape(2.dp)
                            )
                    )
                    if (it < minOf(player.handSize, 10) - 1) {
                        Spacer(modifier = Modifier.width(2.dp))
                    }
                }
                if (player.handSize > 10) {
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "+${player.handSize - 10}",
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "${player.handSize} 张",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}
