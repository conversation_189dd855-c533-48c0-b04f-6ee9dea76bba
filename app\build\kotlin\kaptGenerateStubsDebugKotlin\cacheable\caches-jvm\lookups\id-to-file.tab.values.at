/ Header Record For PersistentHashMapValueStorage< ;app/src/main/java/com/example/myapplication/MainActivity.ktJ Iapp/src/main/java/com/example/myapplication/data/database/GameDatabase.kt@ ?app/src/main/java/com/example/myapplication/data/models/Card.ktG Fapp/src/main/java/com/example/myapplication/data/models/CardPattern.ktE Dapp/src/main/java/com/example/myapplication/data/models/GameState.ktB Aapp/src/main/java/com/example/myapplication/data/models/Player.ktP Oapp/src/main/java/com/example/myapplication/data/preferences/SettingsManager.ktN Mapp/src/main/java/com/example/myapplication/data/repository/GameRepository.kt? >app/src/main/java/com/example/myapplication/domain/AISystem.kt@ ?app/src/main/java/com/example/myapplication/domain/CardLogic.ktA @app/src/main/java/com/example/myapplication/domain/GameEngine.ktG Fapp/src/main/java/com/example/myapplication/domain/GameStateManager.ktD Capp/src/main/java/com/example/myapplication/domain/PlayerManager.ktK Japp/src/main/java/com/example/myapplication/ui/animation/GameAnimations.ktL Kapp/src/main/java/com/example/myapplication/ui/components/CardComponents.ktL Kapp/src/main/java/com/example/myapplication/ui/components/GameComponents.ktE Dapp/src/main/java/com/example/myapplication/ui/screens/GameScreen.ktE Dapp/src/main/java/com/example/myapplication/ui/screens/MainScreen.ktE Dapp/src/main/java/com/example/myapplication/ui/sound/SoundManager.kt> =app/src/main/java/com/example/myapplication/ui/theme/Color.kt> =app/src/main/java/com/example/myapplication/ui/theme/Theme.ktE Dapp/src/main/java/com/example/myapplication/ui/theme/ThemeManager.kt= <app/src/main/java/com/example/myapplication/ui/theme/Type.ktJ Iapp/src/main/java/com/example/myapplication/ui/viewmodel/GameViewModel.kt