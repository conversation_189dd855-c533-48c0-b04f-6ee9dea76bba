package com.example.myapplication.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class GameDatabase_Impl extends GameDatabase {
  private volatile GameHistoryDao _gameHistoryDao;

  private volatile GameSettingsDao _gameSettingsDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(1) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `game_history` (`gameId` TEXT NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER NOT NULL, `duration` INTEGER NOT NULL, `result` TEXT NOT NULL, `winnerName` TEXT NOT NULL, `playerNames` TEXT NOT NULL, `playerScores` TEXT NOT NULL, `bombCount` INTEGER NOT NULL, `isSpring` INTEGER NOT NULL, `landlordIndex` INTEGER NOT NULL, `maxCallingScore` INTEGER NOT NULL, PRIMARY KEY(`gameId`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `game_settings` (`id` INTEGER NOT NULL, `playerName` TEXT NOT NULL, `soundEnabled` INTEGER NOT NULL, `musicEnabled` INTEGER NOT NULL, `aiDifficulty1` TEXT NOT NULL, `aiDifficulty2` TEXT NOT NULL, `theme` TEXT NOT NULL, `animationEnabled` INTEGER NOT NULL, `autoPlay` INTEGER NOT NULL, `gameSpeed` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'c8d820a030c672d7c32cc5d9bde6883b')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `game_history`");
        db.execSQL("DROP TABLE IF EXISTS `game_settings`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsGameHistory = new HashMap<String, TableInfo.Column>(12);
        _columnsGameHistory.put("gameId", new TableInfo.Column("gameId", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("startTime", new TableInfo.Column("startTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("endTime", new TableInfo.Column("endTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("duration", new TableInfo.Column("duration", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("result", new TableInfo.Column("result", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("winnerName", new TableInfo.Column("winnerName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("playerNames", new TableInfo.Column("playerNames", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("playerScores", new TableInfo.Column("playerScores", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("bombCount", new TableInfo.Column("bombCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("isSpring", new TableInfo.Column("isSpring", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("landlordIndex", new TableInfo.Column("landlordIndex", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameHistory.put("maxCallingScore", new TableInfo.Column("maxCallingScore", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysGameHistory = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesGameHistory = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoGameHistory = new TableInfo("game_history", _columnsGameHistory, _foreignKeysGameHistory, _indicesGameHistory);
        final TableInfo _existingGameHistory = TableInfo.read(db, "game_history");
        if (!_infoGameHistory.equals(_existingGameHistory)) {
          return new RoomOpenHelper.ValidationResult(false, "game_history(com.example.myapplication.data.database.GameHistoryEntity).\n"
                  + " Expected:\n" + _infoGameHistory + "\n"
                  + " Found:\n" + _existingGameHistory);
        }
        final HashMap<String, TableInfo.Column> _columnsGameSettings = new HashMap<String, TableInfo.Column>(10);
        _columnsGameSettings.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameSettings.put("playerName", new TableInfo.Column("playerName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameSettings.put("soundEnabled", new TableInfo.Column("soundEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameSettings.put("musicEnabled", new TableInfo.Column("musicEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameSettings.put("aiDifficulty1", new TableInfo.Column("aiDifficulty1", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameSettings.put("aiDifficulty2", new TableInfo.Column("aiDifficulty2", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameSettings.put("theme", new TableInfo.Column("theme", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameSettings.put("animationEnabled", new TableInfo.Column("animationEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameSettings.put("autoPlay", new TableInfo.Column("autoPlay", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGameSettings.put("gameSpeed", new TableInfo.Column("gameSpeed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysGameSettings = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesGameSettings = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoGameSettings = new TableInfo("game_settings", _columnsGameSettings, _foreignKeysGameSettings, _indicesGameSettings);
        final TableInfo _existingGameSettings = TableInfo.read(db, "game_settings");
        if (!_infoGameSettings.equals(_existingGameSettings)) {
          return new RoomOpenHelper.ValidationResult(false, "game_settings(com.example.myapplication.data.database.GameSettingsEntity).\n"
                  + " Expected:\n" + _infoGameSettings + "\n"
                  + " Found:\n" + _existingGameSettings);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "c8d820a030c672d7c32cc5d9bde6883b", "d33beef1e465dfcad46bbf6b8b40d90a");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "game_history","game_settings");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `game_history`");
      _db.execSQL("DELETE FROM `game_settings`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(GameHistoryDao.class, GameHistoryDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(GameSettingsDao.class, GameSettingsDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public GameHistoryDao gameHistoryDao() {
    if (_gameHistoryDao != null) {
      return _gameHistoryDao;
    } else {
      synchronized(this) {
        if(_gameHistoryDao == null) {
          _gameHistoryDao = new GameHistoryDao_Impl(this);
        }
        return _gameHistoryDao;
      }
    }
  }

  @Override
  public GameSettingsDao gameSettingsDao() {
    if (_gameSettingsDao != null) {
      return _gameSettingsDao;
    } else {
      synchronized(this) {
        if(_gameSettingsDao == null) {
          _gameSettingsDao = new GameSettingsDao_Impl(this);
        }
        return _gameSettingsDao;
      }
    }
  }
}
