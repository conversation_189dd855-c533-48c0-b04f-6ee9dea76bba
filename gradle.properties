# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true

# 抑制SDK版本警告
android.suppressUnsupportedCompileSdk=33

# 优化构建性能
android.enableJetifier=true
android.enableR8.fullMode=true
org.gradle.caching=true

# 网络配置
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true

# 离线模式支持
# org.gradle.offline=true

# Kapt优化
kapt.incremental.apt=true
kapt.use.worker.api=true

# 解决SSL问题 - 禁用SSL验证（仅开发环境）
systemProp.javax.net.ssl.trustStore=NONE
systemProp.javax.net.ssl.trustStoreType=Windows-ROOT
systemProp.javax.net.ssl.trustStorePassword=
systemProp.javax.net.ssl.keyStore=NONE
systemProp.javax.net.ssl.keyStoreType=Windows-ROOT
systemProp.javax.net.ssl.keyStorePassword=

# 禁用主机名验证
systemProp.com.sun.net.ssl.checkRevocation=false
systemProp.sun.security.ssl.allowUnsafeRenegotiation=true
systemProp.sun.security.ssl.allowLegacyHelloMessages=true

# HTTP/HTTPS代理设置（如果需要）
# systemProp.http.proxyHost=127.0.0.1
# systemProp.http.proxyPort=8080
# systemProp.https.proxyHost=127.0.0.1
# systemProp.https.proxyPort=8080

# 忽略SSL错误
systemProp.javax.net.ssl.HttpsURLConnection.DefaultHostnameVerifier=ALLOW_ALL

# Java 11 配置
org.gradle.java.home=C:\\Program Files\\Eclipse Adoptium\\jdk-*********-hotspot