package com.example.myapplication.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000L\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\"\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a@\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\nH\u0007\u001a\u001e\u0010\u000b\u001a\u00020\u00012\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u001a\"\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u00052\b\b\u0002\u0010\u0014\u001a\u00020\u0015H\u0007\u001a<\u0010\u0016\u001a\u00020\u00012\b\u0010\u0017\u001a\u0004\u0018\u00010\u000e2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00030\u00192\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u001b2\u0006\u0010\u001c\u001a\u00020\u0005H\u0007\u00a8\u0006\u001d"}, d2 = {"CardComponent", "", "card", "Lcom/example/myapplication/data/models/Card;", "isSelected", "", "isEnabled", "size", "Lcom/example/myapplication/ui/components/CardSize;", "onClick", "Lkotlin/Function0;", "OpponentAreas", "players", "", "Lcom/example/myapplication/data/models/Player;", "gameState", "Lcom/example/myapplication/data/models/GameState;", "OpponentCard", "player", "isCurrentPlayer", "modifier", "Landroidx/compose/ui/Modifier;", "PlayerHandArea", "humanPlayer", "selectedCards", "", "onCardClick", "Lkotlin/Function1;", "canPlay", "app_debug"})
public final class CardComponentsKt {
    
    /**
     * 扑克牌组件
     */
    @androidx.compose.runtime.Composable()
    public static final void CardComponent(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.Card card, boolean isSelected, boolean isEnabled, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.components.CardSize size, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    /**
     * 玩家手牌区域
     */
    @androidx.compose.runtime.Composable()
    public static final void PlayerHandArea(@org.jetbrains.annotations.Nullable()
    com.example.myapplication.data.models.Player humanPlayer, @org.jetbrains.annotations.NotNull()
    java.util.Set<com.example.myapplication.data.models.Card> selectedCards, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.myapplication.data.models.Card, kotlin.Unit> onCardClick, boolean canPlay) {
    }
    
    /**
     * 对手区域
     */
    @androidx.compose.runtime.Composable()
    public static final void OpponentAreas(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Player> players, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GameState gameState) {
    }
    
    /**
     * 对手信息卡片
     */
    @androidx.compose.runtime.Composable()
    public static final void OpponentCard(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.Player player, boolean isCurrentPlayer, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}