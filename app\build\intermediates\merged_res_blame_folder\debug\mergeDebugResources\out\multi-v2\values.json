{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-61:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b5ad589dfae2725b355be2db74e092c\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "79,83", "startColumns": "4,4", "startOffsets": "4896,5073", "endColumns": "53,66", "endOffsets": "4945,5135"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\MyApplication2\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "90", "endOffsets": "142"}, "to": {"startLines": "221", "startColumns": "4", "startOffsets": "13922", "endColumns": "89", "endOffsets": "14007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c7d5ee2d09778517c279ba91c66e1189\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "107", "startColumns": "4", "startOffsets": "6355", "endColumns": "49", "endOffsets": "6400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2de674196157711cb5ad65d1ed732eaa\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "6198", "endColumns": "42", "endOffsets": "6236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed31eac744d4fa401682afb1e43f594a\\transformed\\navigation-runtime-2.7.5\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "82,224,370,373", "startColumns": "4,4,4,4", "startOffsets": "5020,14151,19558,19673", "endLines": "82,230,372,375", "endColumns": "52,24,24,24", "endOffsets": "5068,14450,19668,19783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\87bf3b6ed36062e83c10ffe48a91bc57\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "6301", "endColumns": "53", "endOffsets": "6350"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\MyApplication2\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "51", "endOffsets": "63"}, "to": {"startLines": "111", "startColumns": "4", "startOffsets": "6611", "endColumns": "51", "endOffsets": "6658"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\MyApplication2\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "5,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,675,722,769,816,861,906", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,717,764,811,856,901,943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f2db81535833d6680e5b05d3551a766\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "103", "startColumns": "4", "startOffsets": "6155", "endColumns": "42", "endOffsets": "6193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca8518b89e1044a7f88162fa21c7ddc2\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "84,105", "startColumns": "4,4", "startOffsets": "5140,6241", "endColumns": "41,59", "endOffsets": "5177,6296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6dce069ed5f76821f2d9938d1900d2a1\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,80,81,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,109,112,113,114,115,116,117,118,194,210,211,215,216,220,222,223,231,237,247,282,303,336", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,543,612,948,1018,1086,1158,1228,1289,1363,1436,1497,1558,1620,1684,1746,1807,1875,1975,2035,2101,2174,2243,2300,2352,2414,2486,2562,2627,2686,2745,2805,2865,2925,2985,3045,3105,3165,3225,3285,3345,3404,3464,3524,3584,3644,3704,3764,3824,3884,3944,4004,4063,4123,4183,4242,4301,4360,4419,4478,4950,4985,5182,5237,5300,5355,5413,5469,5527,5588,5651,5708,5759,5817,5867,5928,5985,6051,6085,6120,6458,6663,6730,6802,6871,6940,7014,7086,12391,13164,13281,13482,13592,13793,14012,14084,14455,14658,14959,16765,17446,18128", "endLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,80,81,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,109,112,113,114,115,116,117,118,194,210,214,215,219,220,222,223,236,246,281,302,335,341", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,607,670,1013,1081,1153,1223,1284,1358,1431,1492,1553,1615,1679,1741,1802,1870,1970,2030,2096,2169,2238,2295,2347,2409,2481,2557,2622,2681,2740,2800,2860,2920,2980,3040,3100,3160,3220,3280,3340,3399,3459,3519,3579,3639,3699,3759,3819,3879,3939,3999,4058,4118,4178,4237,4296,4355,4414,4473,4532,4980,5015,5232,5295,5350,5408,5464,5522,5583,5646,5703,5754,5812,5862,5923,5980,6046,6080,6115,6150,6523,6725,6797,6866,6935,7009,7081,7169,12457,13276,13477,13587,13788,13917,14079,14146,14653,14954,16760,17441,18123,18290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\965fb6c3bf340bea42b1b1c81d253a97\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "198,199", "startColumns": "4,4", "startOffsets": "12635,12691", "endColumns": "55,54", "endOffsets": "12686,12741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\47ef16df4bb5bf327dd69a12d73ed8b7\\transformed\\navigation-common-2.7.5\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "342,355,361,367,376", "startColumns": "4,4,4,4,4", "startOffsets": "18295,18934,19178,19425,19788", "endLines": "354,360,366,369,380", "endColumns": "24,24,24,24,24", "endOffsets": "18929,19173,19420,19553,19965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ba551d6c70048f5177e14832f961c8b\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "76", "startColumns": "4", "startOffsets": "4724", "endColumns": "65", "endOffsets": "4785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3bdae484d8629b91b71d2a8e711d605e\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "126,127,128,129,130,131,132,133,134,135,138,139,140,141,142,143,144,145,146,147,148,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7630,7718,7804,7885,7969,8038,8103,8186,8292,8378,8498,8552,8621,8682,8751,8840,8935,9009,9106,9199,9297,9446,9537,9625,9721,9819,9883,9951,10038,10132,10199,10271,10343,10444,10553,10629,10698,10746,10812,10876,10933,10990,11062,11112,11166,11237,11308,11378,11447,11505,11581,11652,11726,11812,11862,11932", "endLines": "126,127,128,129,130,131,132,133,134,137,138,139,140,141,142,143,144,145,146,147,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "7713,7799,7880,7964,8033,8098,8181,8287,8373,8493,8547,8616,8677,8746,8835,8930,9004,9101,9194,9292,9441,9532,9620,9716,9814,9878,9946,10033,10127,10194,10266,10338,10439,10548,10624,10693,10741,10807,10871,10928,10985,11057,11107,11161,11232,11303,11373,11442,11500,11576,11647,11721,11807,11857,11927,11992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f11933d2f9c817336fdb6ff83a5031f8\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "73,74,75,77,78,108,119,120,121,122,123,124,125,186,187,188,189,190,191,192,193,195,196,197,200,203,206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4537,4611,4669,4790,4841,6405,7174,7239,7293,7359,7460,7518,7570,11997,12059,12113,12163,12217,12263,12309,12351,12462,12509,12545,12746,12858,12969", "endLines": "73,74,75,77,78,108,119,120,121,122,123,124,125,186,187,188,189,190,191,192,193,195,196,197,202,205,209", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "4606,4664,4719,4836,4891,6453,7234,7288,7354,7455,7513,7565,7625,12054,12108,12158,12212,12258,12304,12346,12386,12504,12540,12630,12853,12964,13159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f74880b7e4dc2ea23396745126b8a52\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "6528", "endColumns": "82", "endOffsets": "6606"}}]}]}