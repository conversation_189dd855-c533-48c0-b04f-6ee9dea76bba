package com.example.myapplication.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.data.models.*
import com.example.myapplication.domain.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 游戏主ViewModel
 * 负责管理游戏状态和业务逻辑
 */
class GameViewModel : ViewModel() {
    
    private val gameEngine = GameEngine()
    private val cardLogic = CardLogic()
    
    // 游戏状态
    val gameState = gameEngine.gameState.asStateFlow()
    val players = gameEngine.players.asStateFlow()
    val landlordCards = gameEngine.landlordCards.asStateFlow()
    
    // UI状态
    private val _uiState = MutableStateFlow(GameUiState())
    val uiState: StateFlow<GameUiState> = _uiState.asStateFlow()
    
    // 选中的牌
    private val _selectedCards = MutableStateFlow<Set<Card>>(emptySet())
    val selectedCards: StateFlow<Set<Card>> = _selectedCards.asStateFlow()
    
    // 可能的出牌选项
    private val _possiblePlays = MutableStateFlow<List<List<Card>>>(emptyList())
    val possiblePlays: StateFlow<List<List<Card>>> = _possiblePlays.asStateFlow()
    
    // 叫地主分数
    private val _callingScores = MutableStateFlow<Map<Int, Int>>(emptyMap())
    val callingScores: StateFlow<Map<Int, Int>> = _callingScores.asStateFlow()
    
    /**
     * UI状态数据类
     */
    data class GameUiState(
        val isLoading: Boolean = false,
        val showCallingDialog: Boolean = false,
        val showGameOverDialog: Boolean = false,
        val showSettingsDialog: Boolean = false,
        val errorMessage: String? = null,
        val currentPlayerName: String = "",
        val lastPlayInfo: String = "",
        val canPlay: Boolean = false,
        val canPass: Boolean = false,
        val canCallLandlord: Boolean = false
    )
    
    init {
        // 监听游戏状态变化
        viewModelScope.launch {
            gameState.collect { state ->
                updateUiState(state)
            }
        }
        
        // 监听玩家变化
        viewModelScope.launch {
            players.collect { playerList ->
                updatePossiblePlays(playerList)
            }
        }
    }
    
    /**
     * 开始新游戏
     */
    fun startNewGame(
        humanPlayerName: String = "玩家",
        aiDifficulty1: PlayerType = PlayerType.AI_MEDIUM,
        aiDifficulty2: PlayerType = PlayerType.AI_MEDIUM
    ) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                gameEngine.initializeGame(humanPlayerName, aiDifficulty1, aiDifficulty2)
                clearSelectedCards()
                _callingScores.value = emptyMap()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showCallingDialog = true,
                    errorMessage = null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "游戏初始化失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 叫地主
     */
    fun callLandlord(playerIndex: Int, score: Int) {
        viewModelScope.launch {
            try {
                val success = gameEngine.callLandlord(playerIndex, score)
                if (success) {
                    val newScores = _callingScores.value.toMutableMap()
                    newScores[playerIndex] = score
                    _callingScores.value = newScores
                    
                    // 检查是否所有玩家都已叫过地主
                    val allCalled = players.value.all { it.hasCalledLandlord }
                    if (allCalled) {
                        _uiState.value = _uiState.value.copy(showCallingDialog = false)
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "叫地主失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "叫地主出错: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 出牌
     */
    fun playCards(playerIndex: Int, cards: List<Card>) {
        viewModelScope.launch {
            try {
                val success = gameEngine.playCards(playerIndex, cards)
                if (success) {
                    clearSelectedCards()
                    
                    // 检查游戏是否结束
                    if (gameState.value.isGameOver) {
                        _uiState.value = _uiState.value.copy(showGameOverDialog = true)
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "出牌失败，请检查牌型"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "出牌出错: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 过牌
     */
    fun pass(playerIndex: Int) {
        viewModelScope.launch {
            try {
                val success = gameEngine.pass(playerIndex)
                if (success) {
                    clearSelectedCards()
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "过牌失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "过牌出错: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 选择/取消选择牌
     */
    fun toggleCardSelection(card: Card) {
        val currentSelected = _selectedCards.value.toMutableSet()
        if (currentSelected.contains(card)) {
            currentSelected.remove(card)
        } else {
            currentSelected.add(card)
        }
        _selectedCards.value = currentSelected
    }
    
    /**
     * 清空选中的牌
     */
    fun clearSelectedCards() {
        _selectedCards.value = emptySet()
    }
    
    /**
     * 自动选择牌型
     */
    fun autoSelectCards(cards: List<Card>) {
        _selectedCards.value = cards.toSet()
    }
    
    /**
     * 获取选中牌的牌型
     */
    fun getSelectedPattern(): CardPattern {
        return cardLogic.identifyPattern(_selectedCards.value.toList())
    }
    
    /**
     * 检查选中的牌是否可以出
     */
    fun canPlaySelectedCards(): Boolean {
        val selected = _selectedCards.value.toList()
        if (selected.isEmpty()) return false
        
        val pattern = cardLogic.identifyPattern(selected)
        if (!pattern.isValid) return false
        
        val lastPattern = gameState.value.lastPlayedPattern
        return pattern.canBeat(lastPattern)
    }
    
    /**
     * 重新开始游戏
     */
    fun restartGame() {
        viewModelScope.launch {
            gameEngine.restartGame()
            clearSelectedCards()
            _callingScores.value = emptyMap()
            _uiState.value = GameUiState(showCallingDialog = true)
        }
    }
    
    /**
     * 关闭对话框
     */
    fun dismissDialog(dialogType: String) {
        _uiState.value = when (dialogType) {
            "calling" -> _uiState.value.copy(showCallingDialog = false)
            "gameOver" -> _uiState.value.copy(showGameOverDialog = false)
            "settings" -> _uiState.value.copy(showSettingsDialog = false)
            else -> _uiState.value
        }
    }
    
    /**
     * 显示设置对话框
     */
    fun showSettings() {
        _uiState.value = _uiState.value.copy(showSettingsDialog = true)
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * 更新UI状态
     */
    private fun updateUiState(state: GameState) {
        val currentPlayer = gameEngine.getCurrentPlayer()
        val humanPlayer = players.value.find { it.isHuman }
        
        _uiState.value = _uiState.value.copy(
            currentPlayerName = currentPlayer?.name ?: "",
            lastPlayInfo = formatLastPlayInfo(state),
            canPlay = state.isGameActive && currentPlayer?.isHuman == true,
            canPass = state.isGameActive && currentPlayer?.isHuman == true && 
                     state.lastPlayedPattern != null,
            canCallLandlord = state.isCallingPhase && currentPlayer?.isHuman == true &&
                            !(humanPlayer?.hasCalledLandlord ?: true)
        )
    }
    
    /**
     * 更新可能的出牌选项
     */
    private fun updatePossiblePlays(playerList: List<Player>) {
        val humanPlayer = playerList.find { it.isHuman }
        if (humanPlayer != null) {
            val humanIndex = playerList.indexOf(humanPlayer)
            _possiblePlays.value = gameEngine.getPossiblePlays(humanIndex)
        }
    }
    
    /**
     * 格式化最后出牌信息
     */
    private fun formatLastPlayInfo(state: GameState): String {
        return if (state.lastPlayedCards.isNotEmpty() && state.lastPlayerIndex >= 0) {
            val playerName = players.value.getOrNull(state.lastPlayerIndex)?.name ?: "未知"
            val pattern = state.lastPlayedPattern?.type?.displayName ?: "未知牌型"
            "$playerName 出了 $pattern"
        } else {
            "等待出牌"
        }
    }
}
