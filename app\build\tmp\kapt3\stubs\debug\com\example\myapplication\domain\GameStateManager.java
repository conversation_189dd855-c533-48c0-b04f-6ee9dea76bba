package com.example.myapplication.domain;

/**
 * 游戏状态管理器
 * 负责管理游戏的各个阶段和状态转换
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001:\u0001CB\u0005\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0006J\u000e\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0006J\u001c\u0010\u0018\u001a\u00020\u00152\u0006\u0010\u0019\u001a\u00020\u001a2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\nJ\u0006\u0010\u001b\u001a\u00020\u001cJ\u0016\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u00062\u0006\u0010\u001f\u001a\u00020 J\u0010\u0010!\u001a\u00020\u001c2\b\b\u0002\u0010\"\u001a\u00020\u0006J\u0006\u0010#\u001a\u00020\u001cJ\u000e\u0010$\u001a\u00020\u001c2\u0006\u0010%\u001a\u00020\u0006J\u0006\u0010&\u001a\u00020\'J\u0006\u0010(\u001a\u00020\u0006J\u0012\u0010)\u001a\u000e\u0012\u0004\u0012\u00020*\u0012\u0004\u0012\u00020\u00010\u0005J\r\u0010+\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0002\u0010,J\u0006\u0010-\u001a\u00020\u0006J \u0010.\u001a\u001c\u0012\n\u0012\b\u0012\u0004\u0012\u0002000\n\u0012\u0006\u0012\u0004\u0018\u000101\u0012\u0004\u0012\u00020\u00060/J\u0006\u00102\u001a\u00020\u0006J\u0010\u00103\u001a\u00020\u001c2\b\b\u0002\u00104\u001a\u00020\u0006J\u0016\u00105\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00062\u0006\u00106\u001a\u00020\u0006J\u0016\u00107\u001a\u00020\u001c2\u0006\u0010\u0016\u001a\u00020\u00062\u0006\u00108\u001a\u00020*J,\u00109\u001a\u00020\u001c2\u0006\u0010\u0016\u001a\u00020\u00062\u0006\u00108\u001a\u00020*2\f\u0010:\u001a\b\u0012\u0004\u0012\u0002000\n2\u0006\u0010;\u001a\u000201J\u0006\u0010<\u001a\u00020\u001cJ\u001c\u0010=\u001a\u00020\u001c2\u0006\u0010\u0016\u001a\u00020\u00062\f\u0010>\u001a\b\u0012\u0004\u0012\u0002000\nJ\u0006\u0010?\u001a\u00020\u001cJ\u001c\u0010@\u001a\u00020\u001c2\u0012\u0010A\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b0BH\u0002R \u0010\u0003\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R#\u0010\f\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u00050\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u001d\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000f\u00a8\u0006D"}, d2 = {"Lcom/example/myapplication/domain/GameStateManager;", "", "()V", "_callingScores", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "", "_gameState", "Lcom/example/myapplication/data/models/GameState;", "_playHistory", "", "Lcom/example/myapplication/domain/GameStateManager$PlayRecord;", "callingScores", "Lkotlinx/coroutines/flow/StateFlow;", "getCallingScores", "()Lkotlinx/coroutines/flow/StateFlow;", "gameState", "getGameState", "playHistory", "getPlayHistory", "canCallLandlord", "", "playerIndex", "canPlayCards", "checkSpring", "winnerRole", "Lcom/example/myapplication/data/models/PlayerRole;", "clearLastPlay", "", "endGame", "winnerIndex", "result", "Lcom/example/myapplication/data/models/GameResult;", "enterCallingPhase", "startPlayerIndex", "enterDealingPhase", "enterPlayingPhase", "landlordIndex", "getCurrentPhase", "Lcom/example/myapplication/data/models/GamePhase;", "getCurrentPlayerIndex", "getGameStats", "", "getHighestCallingPlayer", "()Ljava/lang/Integer;", "getLandlordIndex", "getLastPlayInfo", "Lkotlin/Triple;", "Lcom/example/myapplication/data/models/Card;", "Lcom/example/myapplication/data/models/CardPattern;", "getMaxCallingScore", "nextPlayer", "playerCount", "playerCallLandlord", "score", "playerPass", "playerName", "playerPlayCards", "cards", "pattern", "resetGame", "setLandlord", "landlordCards", "startNewGame", "updateGameState", "update", "Lkotlin/Function1;", "PlayRecord", "app_debug"})
public final class GameStateManager {
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.myapplication.data.models.GameState> _gameState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.data.models.GameState> gameState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Map<java.lang.Integer, java.lang.Integer>> _callingScores = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.Integer, java.lang.Integer>> callingScores = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.myapplication.domain.GameStateManager.PlayRecord>> _playHistory = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.myapplication.domain.GameStateManager.PlayRecord>> playHistory = null;
    
    public GameStateManager() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.data.models.GameState> getGameState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.Integer, java.lang.Integer>> getCallingScores() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.myapplication.domain.GameStateManager.PlayRecord>> getPlayHistory() {
        return null;
    }
    
    /**
     * 开始新游戏
     */
    public final void startNewGame() {
    }
    
    /**
     * 进入发牌阶段
     */
    public final void enterDealingPhase() {
    }
    
    /**
     * 进入叫地主阶段
     */
    public final void enterCallingPhase(int startPlayerIndex) {
    }
    
    /**
     * 进入出牌阶段
     */
    public final void enterPlayingPhase(int landlordIndex) {
    }
    
    /**
     * 结束游戏
     */
    public final void endGame(int winnerIndex, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GameResult result) {
    }
    
    /**
     * 玩家叫地主
     */
    public final boolean playerCallLandlord(int playerIndex, int score) {
        return false;
    }
    
    /**
     * 切换到下一个玩家
     */
    public final void nextPlayer(int playerCount) {
    }
    
    /**
     * 设置地主
     */
    public final void setLandlord(int playerIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> landlordCards) {
    }
    
    /**
     * 玩家出牌
     */
    public final void playerPlayCards(int playerIndex, @org.jetbrains.annotations.NotNull()
    java.lang.String playerName, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.CardPattern pattern) {
    }
    
    /**
     * 玩家过牌
     */
    public final void playerPass(int playerIndex, @org.jetbrains.annotations.NotNull()
    java.lang.String playerName) {
    }
    
    /**
     * 清空上次出牌记录
     */
    public final void clearLastPlay() {
    }
    
    /**
     * 检查是否春天
     */
    public final boolean checkSpring(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerRole winnerRole, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.domain.GameStateManager.PlayRecord> playHistory) {
        return false;
    }
    
    /**
     * 获取当前游戏阶段
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.GamePhase getCurrentPhase() {
        return null;
    }
    
    /**
     * 获取当前玩家索引
     */
    public final int getCurrentPlayerIndex() {
        return 0;
    }
    
    /**
     * 获取地主索引
     */
    public final int getLandlordIndex() {
        return 0;
    }
    
    /**
     * 获取最后出牌信息
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Triple<java.util.List<com.example.myapplication.data.models.Card>, com.example.myapplication.data.models.CardPattern, java.lang.Integer> getLastPlayInfo() {
        return null;
    }
    
    /**
     * 获取游戏统计信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getGameStats() {
        return null;
    }
    
    /**
     * 重置游戏状态
     */
    public final void resetGame() {
    }
    
    /**
     * 是否可以出牌
     */
    public final boolean canPlayCards(int playerIndex) {
        return false;
    }
    
    /**
     * 是否可以叫地主
     */
    public final boolean canCallLandlord(int playerIndex) {
        return false;
    }
    
    /**
     * 获取最高叫地主分数
     */
    public final int getMaxCallingScore() {
        return 0;
    }
    
    /**
     * 获取叫地主最高分的玩家
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getHighestCallingPlayer() {
        return null;
    }
    
    /**
     * 更新游戏状态的辅助方法
     */
    private final void updateGameState(kotlin.jvm.functions.Function1<? super com.example.myapplication.data.models.GameState, com.example.myapplication.data.models.GameState> update) {
    }
    
    /**
     * 出牌记录数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0087\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\u0006\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\nH\u00c6\u0003J\t\u0010\u001c\u001a\u00020\fH\u00c6\u0003JA\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u001f2\b\u0010 \u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010!\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\"\u001a\u00020\u0005H\u00d6\u0001R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017\u00a8\u0006#"}, d2 = {"Lcom/example/myapplication/domain/GameStateManager$PlayRecord;", "", "playerIndex", "", "playerName", "", "cards", "", "Lcom/example/myapplication/data/models/Card;", "pattern", "Lcom/example/myapplication/data/models/CardPattern;", "timestamp", "", "(ILjava/lang/String;Ljava/util/List;Lcom/example/myapplication/data/models/CardPattern;J)V", "getCards", "()Ljava/util/List;", "getPattern", "()Lcom/example/myapplication/data/models/CardPattern;", "getPlayerIndex", "()I", "getPlayerName", "()Ljava/lang/String;", "getTimestamp", "()J", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
    public static final class PlayRecord {
        private final int playerIndex = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String playerName = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.example.myapplication.data.models.Card> cards = null;
        @org.jetbrains.annotations.NotNull()
        private final com.example.myapplication.data.models.CardPattern pattern = null;
        private final long timestamp = 0L;
        
        public PlayRecord(int playerIndex, @org.jetbrains.annotations.NotNull()
        java.lang.String playerName, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.myapplication.data.models.Card> cards, @org.jetbrains.annotations.NotNull()
        com.example.myapplication.data.models.CardPattern pattern, long timestamp) {
            super();
        }
        
        public final int getPlayerIndex() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getPlayerName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.myapplication.data.models.Card> getCards() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.CardPattern getPattern() {
            return null;
        }
        
        public final long getTimestamp() {
            return 0L;
        }
        
        public final int component1() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.myapplication.data.models.Card> component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.data.models.CardPattern component4() {
            return null;
        }
        
        public final long component5() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.domain.GameStateManager.PlayRecord copy(int playerIndex, @org.jetbrains.annotations.NotNull()
        java.lang.String playerName, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.myapplication.data.models.Card> cards, @org.jetbrains.annotations.NotNull()
        com.example.myapplication.data.models.CardPattern pattern, long timestamp) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}