package com.example.myapplication.ui.sound;

/**
 * 音效管理器
 * 负责管理游戏中的音效和震动
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0010\n\u0002\u0010\u0007\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0016\n\u0000\n\u0002\u0010\u0015\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001:\u0002FGB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\n\u0010\u001c\u001a\u0004\u0018\u00010\u001bH\u0002J\b\u0010\u001d\u001a\u00020\u001eH\u0002J\b\u0010\u001f\u001a\u00020\u001eH\u0002J\u0006\u0010 \u001a\u00020\u001eJ\u001a\u0010!\u001a\u00020\u001e2\b\b\u0001\u0010\"\u001a\u00020\u00152\b\b\u0002\u0010#\u001a\u00020\u0007J\u0006\u0010$\u001a\u00020\u001eJ\u0006\u0010%\u001a\u00020\u001eJ\u0006\u0010&\u001a\u00020\u001eJ\u0006\u0010\'\u001a\u00020\u001eJ\u0006\u0010(\u001a\u00020\u001eJ\u0006\u0010)\u001a\u00020\u001eJ\u0006\u0010*\u001a\u00020\u001eJ\u0006\u0010+\u001a\u00020\u001eJ\u0018\u0010,\u001a\u00020\u001e2\u0006\u0010-\u001a\u00020\u00142\b\b\u0002\u0010.\u001a\u00020/J\u0006\u00100\u001a\u00020\u001eJ\u0006\u00101\u001a\u00020\u001eJ\u0006\u00102\u001a\u00020\u001eJ\u0006\u00103\u001a\u00020\u001eJ\u000e\u00104\u001a\u00020\u001e2\u0006\u0010.\u001a\u00020/J\u000e\u00105\u001a\u00020\u001e2\u0006\u00106\u001a\u00020\u0007J\u000e\u00107\u001a\u00020\u001e2\u0006\u00106\u001a\u00020\u0007J\u000e\u00108\u001a\u00020\u001e2\u0006\u00106\u001a\u00020\u0007J\u0006\u00109\u001a\u00020\u001eJ\u000e\u0010:\u001a\u00020\u001e2\u0006\u0010;\u001a\u00020<J\u0006\u0010=\u001a\u00020\u001eJ\u0006\u0010>\u001a\u00020\u001eJ\u0006\u0010?\u001a\u00020\u001eJ\u001a\u0010@\u001a\u00020\u001e2\u0006\u0010A\u001a\u00020B2\n\b\u0002\u0010C\u001a\u0004\u0018\u00010DJ\u0006\u0010E\u001a\u00020\u001eR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u001a\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00150\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00070\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u000fR\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u001bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006H"}, d2 = {"Lcom/example/myapplication/ui/sound/SoundManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_musicEnabled", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_soundEnabled", "_vibrationEnabled", "backgroundMusicPlayer", "Landroid/media/MediaPlayer;", "musicEnabled", "Lkotlinx/coroutines/flow/StateFlow;", "getMusicEnabled", "()Lkotlinx/coroutines/flow/StateFlow;", "soundEnabled", "getSoundEnabled", "soundMap", "", "Lcom/example/myapplication/ui/sound/SoundManager$SoundType;", "", "soundPool", "Landroid/media/SoundPool;", "vibrationEnabled", "getVibrationEnabled", "vibrator", "Landroid/os/Vibrator;", "getVibrator", "initializeSoundPool", "", "loadSounds", "pauseBackgroundMusic", "playBackgroundMusic", "musicRes", "loop", "playBombSound", "playButtonClick", "playCallLandlordSound", "playCardSound", "playDealSound", "playLoseSound", "playPassSound", "playShuffleSound", "playSound", "soundType", "volume", "", "playWarningSound", "playWinSound", "release", "resumeBackgroundMusic", "setBackgroundMusicVolume", "setMusicEnabled", "enabled", "setSoundEnabled", "setVibrationEnabled", "stopBackgroundMusic", "vibrate", "vibrationType", "Lcom/example/myapplication/ui/sound/SoundManager$VibrationType;", "vibrateBomb", "vibrateLight", "vibrateMedium", "vibratePattern", "pattern", "", "amplitudes", "", "vibrateStrong", "SoundType", "VibrationType", "app_debug"})
public final class SoundManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.Nullable()
    private android.media.SoundPool soundPool;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaPlayer backgroundMusicPlayer;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<com.example.myapplication.ui.sound.SoundManager.SoundType, java.lang.Integer> soundMap = null;
    @org.jetbrains.annotations.Nullable()
    private final android.os.Vibrator vibrator = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _soundEnabled = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> soundEnabled = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _musicEnabled = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> musicEnabled = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _vibrationEnabled = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> vibrationEnabled = null;
    
    public SoundManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getSoundEnabled() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getMusicEnabled() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getVibrationEnabled() {
        return null;
    }
    
    /**
     * 初始化音效池
     */
    private final void initializeSoundPool() {
    }
    
    /**
     * 加载音效文件
     */
    private final void loadSounds() {
    }
    
    /**
     * 播放音效
     */
    public final void playSound(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.sound.SoundManager.SoundType soundType, float volume) {
    }
    
    /**
     * 播放背景音乐
     */
    public final void playBackgroundMusic(@androidx.annotation.RawRes()
    int musicRes, boolean loop) {
    }
    
    /**
     * 停止背景音乐
     */
    public final void stopBackgroundMusic() {
    }
    
    /**
     * 暂停背景音乐
     */
    public final void pauseBackgroundMusic() {
    }
    
    /**
     * 恢复背景音乐
     */
    public final void resumeBackgroundMusic() {
    }
    
    /**
     * 设置背景音乐音量
     */
    public final void setBackgroundMusicVolume(float volume) {
    }
    
    /**
     * 触发震动
     */
    public final void vibrate(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.sound.SoundManager.VibrationType vibrationType) {
    }
    
    /**
     * 触发复杂震动模式
     */
    public final void vibratePattern(@org.jetbrains.annotations.NotNull()
    long[] pattern, @org.jetbrains.annotations.Nullable()
    int[] amplitudes) {
    }
    
    /**
     * 设置音效开关
     */
    public final void setSoundEnabled(boolean enabled) {
    }
    
    /**
     * 设置音乐开关
     */
    public final void setMusicEnabled(boolean enabled) {
    }
    
    /**
     * 设置震动开关
     */
    public final void setVibrationEnabled(boolean enabled) {
    }
    
    /**
     * 获取震动器
     */
    private final android.os.Vibrator getVibrator() {
        return null;
    }
    
    /**
     * 释放资源
     */
    public final void release() {
    }
    
    /**
     * 游戏相关的便捷方法
     */
    public final void playCardSound() {
    }
    
    public final void playButtonClick() {
    }
    
    public final void playWinSound() {
    }
    
    public final void playLoseSound() {
    }
    
    public final void playBombSound() {
    }
    
    public final void playShuffleSound() {
    }
    
    public final void playDealSound() {
    }
    
    public final void playCallLandlordSound() {
    }
    
    public final void playPassSound() {
    }
    
    public final void playWarningSound() {
    }
    
    public final void vibrateLight() {
    }
    
    public final void vibrateMedium() {
    }
    
    public final void vibrateStrong() {
    }
    
    public final void vibrateBomb() {
    }
    
    /**
     * 音效类型枚举
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\b\n\u0002\b\u000f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011\u00a8\u0006\u0012"}, d2 = {"Lcom/example/myapplication/ui/sound/SoundManager$SoundType;", "", "resourceId", "", "(Ljava/lang/String;II)V", "getResourceId", "()I", "CARD_FLIP", "CARD_PLACE", "BUTTON_CLICK", "GAME_WIN", "GAME_LOSE", "BOMB_EXPLODE", "SHUFFLE_CARDS", "DEAL_CARDS", "CALL_LANDLORD", "PASS", "WARNING", "app_debug"})
    public static enum SoundType {
        /*public static final*/ CARD_FLIP /* = new CARD_FLIP(0) */,
        /*public static final*/ CARD_PLACE /* = new CARD_PLACE(0) */,
        /*public static final*/ BUTTON_CLICK /* = new BUTTON_CLICK(0) */,
        /*public static final*/ GAME_WIN /* = new GAME_WIN(0) */,
        /*public static final*/ GAME_LOSE /* = new GAME_LOSE(0) */,
        /*public static final*/ BOMB_EXPLODE /* = new BOMB_EXPLODE(0) */,
        /*public static final*/ SHUFFLE_CARDS /* = new SHUFFLE_CARDS(0) */,
        /*public static final*/ DEAL_CARDS /* = new DEAL_CARDS(0) */,
        /*public static final*/ CALL_LANDLORD /* = new CALL_LANDLORD(0) */,
        /*public static final*/ PASS /* = new PASS(0) */,
        /*public static final*/ WARNING /* = new WARNING(0) */;
        private final int resourceId = 0;
        
        SoundType(@androidx.annotation.RawRes()
        int resourceId) {
        }
        
        public final int getResourceId() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.myapplication.ui.sound.SoundManager.SoundType> getEntries() {
            return null;
        }
    }
    
    /**
     * 震动类型枚举
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0019\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000e\u00a8\u0006\u000f"}, d2 = {"Lcom/example/myapplication/ui/sound/SoundManager$VibrationType;", "", "duration", "", "amplitude", "", "(Ljava/lang/String;IJI)V", "getAmplitude", "()I", "getDuration", "()J", "LIGHT", "MEDIUM", "STRONG", "BOMB", "app_debug"})
    public static enum VibrationType {
        /*public static final*/ LIGHT /* = new LIGHT(0L, 0) */,
        /*public static final*/ MEDIUM /* = new MEDIUM(0L, 0) */,
        /*public static final*/ STRONG /* = new STRONG(0L, 0) */,
        /*public static final*/ BOMB /* = new BOMB(0L, 0) */;
        private final long duration = 0L;
        private final int amplitude = 0;
        
        VibrationType(long duration, int amplitude) {
        }
        
        public final long getDuration() {
            return 0L;
        }
        
        public final int getAmplitude() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.myapplication.ui.sound.SoundManager.VibrationType> getEntries() {
            return null;
        }
    }
}