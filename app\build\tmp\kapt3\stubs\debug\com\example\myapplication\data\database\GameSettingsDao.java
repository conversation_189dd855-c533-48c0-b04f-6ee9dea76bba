package com.example.myapplication.data.database;

/**
 * 游戏设置DAO
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\b\bg\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u0004\u0018\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u001b\u001a\u00020\u00062\u0006\u0010\u001c\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\f\u00a8\u0006\u001d"}, d2 = {"Lcom/example/myapplication/data/database/GameSettingsDao;", "", "getSettings", "Lcom/example/myapplication/data/database/GameSettingsEntity;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveSettings", "", "settings", "(Lcom/example/myapplication/data/database/GameSettingsEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAIDifficulty1", "difficulty", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAIDifficulty2", "updateAnimationEnabled", "enabled", "", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAutoPlay", "updateGameSpeed", "speed", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateMusicEnabled", "updatePlayerName", "name", "updateSoundEnabled", "updateTheme", "theme", "app_debug"})
@androidx.room.Dao()
public abstract interface GameSettingsDao {
    
    @androidx.room.Query(value = "SELECT * FROM game_settings WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.myapplication.data.database.GameSettingsEntity> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object saveSettings(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.database.GameSettingsEntity settings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE game_settings SET playerName = :name WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePlayerName(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE game_settings SET soundEnabled = :enabled WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateSoundEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE game_settings SET musicEnabled = :enabled WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateMusicEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE game_settings SET aiDifficulty1 = :difficulty WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAIDifficulty1(@org.jetbrains.annotations.NotNull()
    java.lang.String difficulty, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE game_settings SET aiDifficulty2 = :difficulty WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAIDifficulty2(@org.jetbrains.annotations.NotNull()
    java.lang.String difficulty, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE game_settings SET theme = :theme WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateTheme(@org.jetbrains.annotations.NotNull()
    java.lang.String theme, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE game_settings SET animationEnabled = :enabled WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAnimationEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE game_settings SET autoPlay = :enabled WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAutoPlay(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE game_settings SET gameSpeed = :speed WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateGameSpeed(int speed, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}