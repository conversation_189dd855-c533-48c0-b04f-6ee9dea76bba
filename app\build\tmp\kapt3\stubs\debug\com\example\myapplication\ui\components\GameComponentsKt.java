package com.example.myapplication.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a,\u0010\u0006\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\bH\u0007\u001a:\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\b2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u00a8\u0006\u0012"}, d2 = {"CallingDialog", "", "gameViewModel", "Lcom/example/myapplication/ui/viewmodel/GameViewModel;", "onDismiss", "Lkotlin/Function0;", "CentralPlayArea", "lastPlayedCards", "", "Lcom/example/myapplication/data/models/Card;", "lastPlayerIndex", "", "players", "Lcom/example/myapplication/data/models/Player;", "GameOverDialog", "gameState", "Lcom/example/myapplication/data/models/GameState;", "onRestart", "app_debug"})
public final class GameComponentsKt {
    
    /**
     * 中央出牌区域
     */
    @androidx.compose.runtime.Composable()
    public static final void CentralPlayArea(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> lastPlayedCards, int lastPlayerIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Player> players) {
    }
    
    /**
     * 叫地主对话框
     */
    @androidx.compose.runtime.Composable()
    public static final void CallingDialog(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.ui.viewmodel.GameViewModel gameViewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    /**
     * 游戏结束对话框
     */
    @androidx.compose.runtime.Composable()
    public static final void GameOverDialog(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GameState gameState, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Player> players, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRestart, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
}