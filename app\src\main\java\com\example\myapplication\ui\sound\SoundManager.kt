package com.example.myapplication.ui.sound

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.SoundPool
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import androidx.annotation.RawRes
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * 音效管理器
 * 负责管理游戏中的音效和震动
 */
class SoundManager(private val context: Context) {
    
    private var soundPool: SoundPool? = null
    private var backgroundMusicPlayer: MediaPlayer? = null
    private val soundMap = mutableMapOf<SoundType, Int>()
    private val vibrator: Vibrator? = getVibrator()
    
    private val _soundEnabled = MutableStateFlow(true)
    val soundEnabled: StateFlow<Boolean> = _soundEnabled
    
    private val _musicEnabled = MutableStateFlow(true)
    val musicEnabled: StateFlow<Boolean> = _musicEnabled
    
    private val _vibrationEnabled = MutableStateFlow(true)
    val vibrationEnabled: StateFlow<Boolean> = _vibrationEnabled
    
    init {
        initializeSoundPool()
    }
    
    /**
     * 音效类型枚举
     */
    enum class SoundType(@RawRes val resourceId: Int) {
        CARD_FLIP(android.R.raw.card_flip),      // 翻牌音效（使用系统音效作为占位符）
        CARD_PLACE(android.R.raw.card_place),    // 出牌音效
        BUTTON_CLICK(android.R.raw.button_click), // 按钮点击
        GAME_WIN(android.R.raw.game_win),        // 胜利音效
        GAME_LOSE(android.R.raw.game_lose),      // 失败音效
        BOMB_EXPLODE(android.R.raw.bomb_explode), // 炸弹爆炸
        SHUFFLE_CARDS(android.R.raw.shuffle),     // 洗牌音效
        DEAL_CARDS(android.R.raw.deal),          // 发牌音效
        CALL_LANDLORD(android.R.raw.call),       // 叫地主音效
        PASS(android.R.raw.pass),                // 过牌音效
        WARNING(android.R.raw.warning)           // 警告音效（手牌少时）
    }
    
    /**
     * 震动类型枚举
     */
    enum class VibrationType(val duration: Long, val amplitude: Int = VibrationEffect.DEFAULT_AMPLITUDE) {
        LIGHT(50, 100),      // 轻微震动
        MEDIUM(100, 150),    // 中等震动
        STRONG(200, 255),    // 强烈震动
        BOMB(300, 255)       // 炸弹震动
    }
    
    /**
     * 初始化音效池
     */
    private fun initializeSoundPool() {
        val audioAttributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_GAME)
            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
            .build()
        
        soundPool = SoundPool.Builder()
            .setMaxStreams(10)
            .setAudioAttributes(audioAttributes)
            .build()
        
        // 预加载音效（这里使用占位符，实际项目中需要添加真实的音效文件）
        loadSounds()
    }
    
    /**
     * 加载音效文件
     */
    private fun loadSounds() {
        // 注意：这里使用的是占位符资源ID，实际项目中需要：
        // 1. 在 app/src/main/res/raw/ 目录下添加音效文件
        // 2. 更新 SoundType 枚举中的资源ID
        
        SoundType.values().forEach { soundType ->
            try {
                // 由于我们使用的是占位符ID，这里可能会失败
                // 在实际项目中，应该使用真实的音效文件
                val soundId = soundPool?.load(context, soundType.resourceId, 1)
                if (soundId != null && soundId > 0) {
                    soundMap[soundType] = soundId
                }
            } catch (e: Exception) {
                // 忽略加载失败的音效
                println("Failed to load sound: ${soundType.name}")
            }
        }
    }
    
    /**
     * 播放音效
     */
    fun playSound(soundType: SoundType, volume: Float = 1.0f) {
        if (!_soundEnabled.value) return
        
        val soundId = soundMap[soundType]
        if (soundId != null) {
            soundPool?.play(soundId, volume, volume, 1, 0, 1.0f)
        }
    }
    
    /**
     * 播放背景音乐
     */
    fun playBackgroundMusic(@RawRes musicRes: Int, loop: Boolean = true) {
        if (!_musicEnabled.value) return
        
        stopBackgroundMusic()
        
        try {
            backgroundMusicPlayer = MediaPlayer.create(context, musicRes).apply {
                isLooping = loop
                setAudioAttributes(
                    AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_GAME)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build()
                )
                start()
            }
        } catch (e: Exception) {
            println("Failed to play background music: ${e.message}")
        }
    }
    
    /**
     * 停止背景音乐
     */
    fun stopBackgroundMusic() {
        backgroundMusicPlayer?.apply {
            if (isPlaying) {
                stop()
            }
            release()
        }
        backgroundMusicPlayer = null
    }
    
    /**
     * 暂停背景音乐
     */
    fun pauseBackgroundMusic() {
        backgroundMusicPlayer?.pause()
    }
    
    /**
     * 恢复背景音乐
     */
    fun resumeBackgroundMusic() {
        if (_musicEnabled.value) {
            backgroundMusicPlayer?.start()
        }
    }
    
    /**
     * 设置背景音乐音量
     */
    fun setBackgroundMusicVolume(volume: Float) {
        backgroundMusicPlayer?.setVolume(volume, volume)
    }
    
    /**
     * 触发震动
     */
    fun vibrate(vibrationType: VibrationType) {
        if (!_vibrationEnabled.value || vibrator == null) return
        
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val vibrationEffect = VibrationEffect.createOneShot(
                vibrationType.duration,
                vibrationType.amplitude
            )
            vibrator.vibrate(vibrationEffect)
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(vibrationType.duration)
        }
    }
    
    /**
     * 触发复杂震动模式
     */
    fun vibratePattern(pattern: LongArray, amplitudes: IntArray? = null) {
        if (!_vibrationEnabled.value || vibrator == null) return
        
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val vibrationEffect = if (amplitudes != null) {
                VibrationEffect.createWaveform(pattern, amplitudes, -1)
            } else {
                VibrationEffect.createWaveform(pattern, -1)
            }
            vibrator.vibrate(vibrationEffect)
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(pattern, -1)
        }
    }
    
    /**
     * 设置音效开关
     */
    fun setSoundEnabled(enabled: Boolean) {
        _soundEnabled.value = enabled
    }
    
    /**
     * 设置音乐开关
     */
    fun setMusicEnabled(enabled: Boolean) {
        _musicEnabled.value = enabled
        if (!enabled) {
            pauseBackgroundMusic()
        } else {
            resumeBackgroundMusic()
        }
    }
    
    /**
     * 设置震动开关
     */
    fun setVibrationEnabled(enabled: Boolean) {
        _vibrationEnabled.value = enabled
    }
    
    /**
     * 获取震动器
     */
    private fun getVibrator(): Vibrator? {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as? VibratorManager
            vibratorManager?.defaultVibrator
        } else {
            @Suppress("DEPRECATION")
            context.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        soundPool?.release()
        soundPool = null
        stopBackgroundMusic()
        soundMap.clear()
    }
    
    /**
     * 游戏相关的便捷方法
     */
    
    fun playCardSound() = playSound(SoundType.CARD_PLACE)
    fun playButtonClick() = playSound(SoundType.BUTTON_CLICK)
    fun playWinSound() = playSound(SoundType.GAME_WIN)
    fun playLoseSound() = playSound(SoundType.GAME_LOSE)
    fun playBombSound() = playSound(SoundType.BOMB_EXPLODE)
    fun playShuffleSound() = playSound(SoundType.SHUFFLE_CARDS)
    fun playDealSound() = playSound(SoundType.DEAL_CARDS)
    fun playCallLandlordSound() = playSound(SoundType.CALL_LANDLORD)
    fun playPassSound() = playSound(SoundType.PASS)
    fun playWarningSound() = playSound(SoundType.WARNING)
    
    fun vibrateLight() = vibrate(VibrationType.LIGHT)
    fun vibrateMedium() = vibrate(VibrationType.MEDIUM)
    fun vibrateStrong() = vibrate(VibrationType.STRONG)
    fun vibrateBomb() = vibrate(VibrationType.BOMB)
}
