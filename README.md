# 斗地主游戏 Android APP

一个使用 Kotlin 和 Jetpack Compose 开发的完整斗地主游戏应用。

## 项目特性

### 🎮 核心游戏功能
- **完整的斗地主规则实现**：支持标准的三人斗地主游戏
- **54张扑克牌系统**：包含大小王的完整牌组
- **智能牌型识别**：支持单牌、对子、三张、顺子、炸弹、王炸等所有牌型
- **自动洗牌发牌**：使用Fisher-Yates算法确保随机性
- **完整的游戏流程**：叫地主 → 出牌 → 结算

### 🤖 AI对手系统
- **三档难度设置**：
  - 初级AI：随机合法出牌
  - 中级AI：基于牌型优先级决策
  - 高级AI：概率计算和策略分析
- **智能叫地主**：AI会根据手牌强度和底牌价值做出决策
- **策略性出牌**：考虑游戏局势、角色身份和剩余手牌

### 🎨 现代化UI设计
- **Jetpack Compose**：使用最新的声明式UI框架
- **Material Design 3**：遵循最新的设计规范
- **响应式布局**：适配不同屏幕尺寸
- **流畅动画**：卡牌翻转、出牌、爆炸等动画效果
- **主题切换**：支持浅色/深色/自动主题

### 🔊 音效与反馈
- **丰富音效系统**：出牌、炸弹、胜利等音效
- **震动反馈**：支持不同强度的触觉反馈
- **背景音乐**：可选的游戏背景音乐

### 💾 数据持久化
- **Room数据库**：存储游戏历史记录
- **DataStore**：保存用户设置和偏好
- **游戏统计**：胜率、平均游戏时长等数据分析

## 项目架构

### 架构模式
采用 **MVVM + Repository** 模式，确保代码的可维护性和可测试性：

```
┌─────────────────┐
│   UI Layer      │  ← Jetpack Compose + ViewModel
├─────────────────┤
│  Domain Layer   │  ← 业务逻辑 + 用例
├─────────────────┤
│   Data Layer    │  ← Repository + 数据源
└─────────────────┘
```

### 核心模块

#### 1. 数据模型 (`data/models/`)
- `Card.kt` - 扑克牌数据模型
- `Player.kt` - 玩家数据模型
- `GameState.kt` - 游戏状态管理
- `CardPattern.kt` - 牌型定义和比较

#### 2. 业务逻辑 (`domain/`)
- `GameEngine.kt` - 游戏引擎核心
- `CardLogic.kt` - 牌型识别和验证
- `AISystem.kt` - AI决策系统
- `PlayerManager.kt` - 玩家管理
- `GameStateManager.kt` - 状态流转管理

#### 3. 数据层 (`data/`)
- `GameDatabase.kt` - Room数据库定义
- `GameRepository.kt` - 数据仓库
- `SettingsManager.kt` - 设置管理

#### 4. UI层 (`ui/`)
- `screens/` - 界面组件
- `components/` - 可复用UI组件
- `viewmodel/` - ViewModel层
- `theme/` - 主题和样式
- `animation/` - 动画组件
- `sound/` - 音效管理

## 技术栈

### 核心技术
- **Kotlin** - 主要开发语言
- **Jetpack Compose** - 现代化UI框架
- **Coroutines** - 异步编程
- **Flow** - 响应式数据流

### Jetpack组件
- **ViewModel** - UI状态管理
- **Room** - 本地数据库
- **DataStore** - 键值对存储
- **Navigation** - 导航管理

### 第三方库
- **Kotlinx Serialization** - JSON序列化
- **Material3** - UI组件库

## 游戏规则

### 基本规则
1. **三人游戏**：一人当地主，两人当农民
2. **发牌**：每人17张牌，剩余3张作为底牌
3. **叫地主**：玩家轮流叫分（1-3分），最高分者成为地主
4. **出牌**：地主先出，其他玩家依次跟牌或过牌
5. **胜利条件**：先出完手牌的一方获胜

### 牌型说明
- **单牌**：任意一张牌
- **对子**：两张相同点数的牌
- **三张**：三张相同点数的牌
- **三带一**：三张+一张单牌
- **三带二**：三张+一对
- **顺子**：五张或以上连续的牌（不包括2和王）
- **连对**：三对或以上连续的对子
- **飞机**：两个或以上连续的三张
- **炸弹**：四张相同点数的牌
- **王炸**：大王+小王

## 安装和运行

### 环境要求
- Android Studio Arctic Fox 或更高版本
- JDK 11 或更高版本
- Android SDK API 24+ (Android 7.0)

### 构建步骤
1. 克隆项目到本地
2. 使用Android Studio打开项目
3. 等待Gradle同步完成
4. 连接Android设备或启动模拟器
5. 点击运行按钮

### 测试
```bash
# 运行单元测试
./gradlew test

# 运行UI测试
./gradlew connectedAndroidTest
```

## 项目结构

```
app/src/main/java/com/example/myapplication/
├── data/
│   ├── database/          # Room数据库
│   ├── models/           # 数据模型
│   ├── preferences/      # DataStore设置
│   └── repository/       # 数据仓库
├── domain/               # 业务逻辑层
├── ui/
│   ├── animation/        # 动画组件
│   ├── components/       # UI组件
│   ├── screens/         # 界面
│   ├── sound/           # 音效管理
│   ├── theme/           # 主题样式
│   └── viewmodel/       # ViewModel
└── MainActivity.kt      # 主活动
```

## 开发计划

### 已完成功能 ✅
- [x] 项目架构设计与依赖配置
- [x] 游戏基础逻辑模块开发
- [x] 玩家与游戏状态管理
- [x] 用户界面开发
- [x] AI对手系统实现
- [x] 数据存储与配置
- [x] 扩展功能开发（音效、动画、主题）

### 待优化功能 🚧
- [ ] 性能优化和内存管理
- [ ] 更多单元测试和集成测试
- [ ] 网络对战功能
- [ ] 更多AI策略算法
- [ ] 游戏回放功能

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发规范
- 遵循Kotlin编码规范
- 使用有意义的提交信息
- 为新功能添加相应的测试
- 更新相关文档

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至开发者

---

**享受游戏，祝您好运！** 🎮🃏
