package com.example.myapplication.domain

import com.example.myapplication.data.models.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 游戏引擎类
 * 负责管理整个游戏的流程和状态
 */
class GameEngine {

    private val cardLogic = CardLogic()
    private val aiSystem = AISystem()
    
    // 游戏状态
    private val _gameState = MutableStateFlow(GameState.newGame())
    val gameState: StateFlow<GameState> = _gameState.asStateFlow()
    
    // 玩家列表
    private val _players = MutableStateFlow<List<Player>>(emptyList())
    val players: StateFlow<List<Player>> = _players.asStateFlow()
    
    // 底牌
    private val _landlordCards = MutableStateFlow<List<Card>>(emptyList())
    val landlordCards: StateFlow<List<Card>> = _landlordCards.asStateFlow()
    
    /**
     * 初始化游戏
     */
    fun initializeGame(
        humanPlayerName: String = "玩家",
        aiDifficulty1: PlayerType = PlayerType.AI_MEDIUM,
        aiDifficulty2: PlayerType = PlayerType.AI_MEDIUM
    ) {
        val playerList = listOf(
            Player.createHuman(humanPlayerName),
            Player.createAI("ai1", "电脑1", aiDifficulty1, PlayerPosition.LEFT),
            Player.createAI("ai2", "电脑2", aiDifficulty2, PlayerPosition.RIGHT)
        )
        
        _players.value = playerList
        _gameState.value = GameState.newGame()
        
        // 开始发牌
        dealCards()
    }
    
    /**
     * 发牌
     */
    private fun dealCards() {
        val deck = Card.createFullDeck()
        val shuffledDeck = cardLogic.shuffleDeck(deck)
        val bottomCards = cardLogic.dealCards(shuffledDeck, _players.value)
        
        _landlordCards.value = bottomCards
        _gameState.value = _gameState.value.copy(phase = GamePhase.CALLING)
        
        // 随机选择一个玩家开始叫地主
        val startPlayerIndex = (0..2).random()
        _gameState.value = _gameState.value.copy(currentPlayerIndex = startPlayerIndex)
    }
    
    /**
     * 叫地主
     */
    fun callLandlord(playerIndex: Int, score: Int): Boolean {
        val currentState = _gameState.value
        if (currentState.phase != GamePhase.CALLING || 
            currentState.currentPlayerIndex != playerIndex) {
            return false
        }
        
        val player = _players.value[playerIndex]
        player.hasCalledLandlord = true
        
        if (score > currentState.maxCallingScore) {
            val newState = currentState.copy(
                maxCallingScore = score,
                callingPlayerIndex = playerIndex
            )
            _gameState.value = newState
        }
        
        // 检查是否所有玩家都已叫过地主
        val allCalled = _players.value.all { it.hasCalledLandlord }
        if (allCalled) {
            finalizeLandlord()
        } else {
            // 下一个玩家叫地主
            val newState = currentState.copy()
            newState.nextPlayer()
            _gameState.value = newState

            // 如果下一个玩家是AI，自动叫地主
            processAITurn()
        }
        
        return true
    }
    
    /**
     * 确定地主
     */
    private fun finalizeLandlord() {
        val currentState = _gameState.value
        if (currentState.maxCallingScore > 0) {
            val landlordIndex = currentState.callingPlayerIndex
            val landlord = _players.value[landlordIndex]
            
            // 设置角色
            _players.value.forEachIndexed { index, player ->
                player.role = if (index == landlordIndex) PlayerRole.LANDLORD else PlayerRole.FARMER
            }
            
            // 地主获得底牌
            landlord.addCards(_landlordCards.value)
            
            // 更新游戏状态
            _gameState.value = currentState.copy(
                phase = GamePhase.PLAYING,
                landlordIndex = landlordIndex,
                currentPlayerIndex = landlordIndex,
                landlordCards = _landlordCards.value
            )
        } else {
            // 没有人叫地主，重新发牌
            dealCards()
        }
    }
    
    /**
     * 玩家出牌
     */
    fun playCards(playerIndex: Int, cards: List<Card>): Boolean {
        val currentState = _gameState.value
        if (currentState.phase != GamePhase.PLAYING || 
            currentState.currentPlayerIndex != playerIndex) {
            return false
        }
        
        val player = _players.value[playerIndex]
        val pattern = cardLogic.identifyPattern(cards)
        
        // 验证牌型是否有效
        if (!pattern.isValid) return false
        
        // 验证是否可以压过上家的牌
        if (!pattern.canBeat(currentState.lastPlayedPattern)) return false
        
        // 验证玩家是否有这些牌
        if (!player.hand.containsAll(cards)) return false
        
        // 执行出牌
        if (player.playCards(cards)) {
            _gameState.value.playCards(playerIndex, cards, pattern)
            
            // 检查是否获胜
            if (player.hasWon) {
                endGame(playerIndex)
            } else {
                // 下一个玩家
                val newState = _gameState.value.copy()
                newState.nextPlayer()
                _gameState.value = newState

                // 如果下一个玩家是AI，自动出牌
                processAITurn()
            }
            return true
        }
        
        return false
    }
    
    /**
     * 玩家过牌
     */
    fun pass(playerIndex: Int): Boolean {
        val currentState = _gameState.value
        if (currentState.phase != GamePhase.PLAYING || 
            currentState.currentPlayerIndex != playerIndex) {
            return false
        }
        
        val player = _players.value[playerIndex]
        player.passCount++
        
        _gameState.value.pass()
        
        // 下一个玩家
        val newState = _gameState.value.copy()
        newState.nextPlayer()
        _gameState.value = newState

        // 如果下一个玩家是AI，自动出牌
        processAITurn()

        return true
    }
    
    /**
     * 结束游戏
     */
    private fun endGame(winnerIndex: Int) {
        val winner = _players.value[winnerIndex]
        val result = if (winner.isLandlord) {
            GameResult.LANDLORD_WIN
        } else {
            GameResult.FARMERS_WIN
        }
        
        // 检查是否春天
        val isSpring = if (winner.isLandlord) {
            // 地主春天：农民一张牌都没出过
            _players.value.filter { it.isFarmer }.all { it.playedCards.isEmpty() }
        } else {
            // 农民春天：地主只出过一手牌
            _players.value.find { it.isLandlord }?.playedCards?.size == 1
        }
        
        _gameState.value.endGame(winnerIndex, result)
        _gameState.value = _gameState.value.copy(isSpring = isSpring)
        
        // 计算得分
        calculateScores()
    }
    
    /**
     * 计算得分
     */
    private fun calculateScores() {
        val currentState = _gameState.value
        val baseScore = currentState.maxCallingScore
        val bombMultiplier = Math.pow(2.0, currentState.bombCount.toDouble()).toInt()
        val springMultiplier = if (currentState.isSpring) 2 else 1
        
        val finalScore = baseScore * bombMultiplier * springMultiplier
        
        _players.value.forEach { player ->
            player.score = if (currentState.result == GameResult.LANDLORD_WIN) {
                if (player.isLandlord) finalScore * 2 else -finalScore
            } else {
                if (player.isLandlord) -finalScore * 2 else finalScore
            }
        }
    }
    
    /**
     * 重新开始游戏
     */
    fun restartGame() {
        _players.value.forEach { it.reset() }
        _gameState.value.reset()
        dealCards()
    }
    
    /**
     * 获取当前玩家
     */
    fun getCurrentPlayer(): Player? {
        val currentIndex = _gameState.value.currentPlayerIndex
        return if (currentIndex in _players.value.indices) {
            _players.value[currentIndex]
        } else null
    }
    
    /**
     * 获取地主玩家
     */
    fun getLandlordPlayer(): Player? {
        val landlordIndex = _gameState.value.landlordIndex
        return if (landlordIndex >= 0 && landlordIndex < _players.value.size) {
            _players.value[landlordIndex]
        } else null
    }
    
    /**
     * 获取可能的出牌选项
     */
    fun getPossiblePlays(playerIndex: Int): List<List<Card>> {
        if (playerIndex !in _players.value.indices) return emptyList()

        val player = _players.value[playerIndex]
        val targetPattern = _gameState.value.lastPlayedPattern

        return cardLogic.getPossiblePlays(player.hand, targetPattern)
    }

    /**
     * 处理AI回合
     */
    private fun processAITurn() {
        val currentState = _gameState.value
        val currentPlayer = getCurrentPlayer()

        if (currentPlayer?.isAI == true) {
            when (currentState.phase) {
                GamePhase.CALLING -> {
                    // AI叫地主
                    val score = aiSystem.makeCallingDecision(
                        currentPlayer,
                        currentPlayer.hand,
                        currentState.maxCallingScore,
                        _landlordCards.value
                    )

                    // 延迟执行，模拟思考时间
                    CoroutineScope(Dispatchers.Main).launch {
                        delay(1000 + (0..1000).random())
                        callLandlord(_players.value.indexOf(currentPlayer), score)
                    }
                }

                GamePhase.PLAYING -> {
                    // AI出牌
                    val aiPlayCards = aiSystem.makePlayDecision(
                        currentPlayer,
                        currentPlayer.hand,
                        currentState.lastPlayedPattern,
                        currentState,
                        _players.value
                    )

                    // 延迟执行，模拟思考时间
                    CoroutineScope(Dispatchers.Main).launch {
                        delay(1500 + (0..1500).random())

                        if (aiPlayCards != null) {
                            playCards(_players.value.indexOf(currentPlayer), aiPlayCards)
                        } else {
                            pass(_players.value.indexOf(currentPlayer))
                        }
                    }
                }

                else -> { /* 其他阶段不需要AI处理 */ }
            }
        }
    }
}
