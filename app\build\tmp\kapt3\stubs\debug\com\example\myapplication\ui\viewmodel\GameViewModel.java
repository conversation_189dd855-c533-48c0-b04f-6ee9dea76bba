package com.example.myapplication.ui.viewmodel;

/**
 * 游戏主ViewModel
 * 负责管理游戏状态和业务逻辑
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000|\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\"\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001:\u0001FB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0014\u0010%\u001a\u00020&2\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\t0\bJ\u0016\u0010(\u001a\u00020&2\u0006\u0010)\u001a\u00020\u00062\u0006\u0010*\u001a\u00020\u0006J\u0006\u0010+\u001a\u00020,J\u0006\u0010-\u001a\u00020&J\u0006\u0010.\u001a\u00020&J\u000e\u0010/\u001a\u00020&2\u0006\u00100\u001a\u000201J\u0010\u00102\u001a\u0002012\u0006\u00103\u001a\u000204H\u0002J\u0006\u00105\u001a\u000206J\u000e\u00107\u001a\u00020&2\u0006\u0010)\u001a\u00020\u0006J\u001c\u00108\u001a\u00020&2\u0006\u0010)\u001a\u00020\u00062\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\t0\bJ\u0006\u00109\u001a\u00020&J\u0006\u0010:\u001a\u00020&J$\u0010;\u001a\u00020&2\b\b\u0002\u0010<\u001a\u0002012\b\b\u0002\u0010=\u001a\u00020>2\b\b\u0002\u0010?\u001a\u00020>J\u000e\u0010@\u001a\u00020&2\u0006\u0010A\u001a\u00020\tJ\u0016\u0010B\u001a\u00020&2\f\u0010C\u001a\b\u0012\u0004\u0012\u00020D0\bH\u0002J\u0010\u0010E\u001a\u00020&2\u0006\u00103\u001a\u000204H\u0002R \u0010\u0003\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\u0007\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u000b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R#\u0010\u000e\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u00050\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0013\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u0018\u0010\u0019R\u0013\u0010\u001b\u001a\u00020\u0017\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u001c\u0010\u0019R\u0013\u0010\u001d\u001a\u00020\u0017\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u001e\u0010\u0019R#\u0010\u001f\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\b0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0011R\u001d\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u000b0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0011R\u0017\u0010#\u001a\b\u0012\u0004\u0012\u00020\r0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u0011\u00a8\u0006G"}, d2 = {"Lcom/example/myapplication/ui/viewmodel/GameViewModel;", "Landroidx/lifecycle/ViewModel;", "()V", "_callingScores", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "", "_possiblePlays", "", "Lcom/example/myapplication/data/models/Card;", "_selectedCards", "", "_uiState", "Lcom/example/myapplication/ui/viewmodel/GameViewModel$GameUiState;", "callingScores", "Lkotlinx/coroutines/flow/StateFlow;", "getCallingScores", "()Lkotlinx/coroutines/flow/StateFlow;", "cardLogic", "Lcom/example/myapplication/domain/CardLogic;", "gameEngine", "Lcom/example/myapplication/domain/GameEngine;", "gameState", "error/NonExistentClass", "getGameState", "()Lerror/NonExistentClass;", "Lerror/NonExistentClass;", "landlordCards", "getLandlordCards", "players", "getPlayers", "possiblePlays", "getPossiblePlays", "selectedCards", "getSelectedCards", "uiState", "getUiState", "autoSelectCards", "", "cards", "callLandlord", "playerIndex", "score", "canPlaySelectedCards", "", "clearError", "clearSelectedCards", "dismissDialog", "dialogType", "", "formatLastPlayInfo", "state", "Lcom/example/myapplication/data/models/GameState;", "getSelectedPattern", "Lcom/example/myapplication/data/models/CardPattern;", "pass", "playCards", "restartGame", "showSettings", "startNewGame", "humanPlayerName", "aiDifficulty1", "Lcom/example/myapplication/data/models/PlayerType;", "aiDifficulty2", "toggleCardSelection", "card", "updatePossiblePlays", "playerList", "Lcom/example/myapplication/data/models/Player;", "updateUiState", "GameUiState", "app_debug"})
public final class GameViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.domain.GameEngine gameEngine = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.domain.CardLogic cardLogic = null;
    @org.jetbrains.annotations.NotNull()
    private final error.NonExistentClass gameState = null;
    @org.jetbrains.annotations.NotNull()
    private final error.NonExistentClass players = null;
    @org.jetbrains.annotations.NotNull()
    private final error.NonExistentClass landlordCards = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.myapplication.ui.viewmodel.GameViewModel.GameUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.viewmodel.GameViewModel.GameUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Set<com.example.myapplication.data.models.Card>> _selectedCards = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.Set<com.example.myapplication.data.models.Card>> selectedCards = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<java.util.List<com.example.myapplication.data.models.Card>>> _possiblePlays = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<java.util.List<com.example.myapplication.data.models.Card>>> possiblePlays = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Map<java.lang.Integer, java.lang.Integer>> _callingScores = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.Integer, java.lang.Integer>> callingScores = null;
    
    public GameViewModel() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final error.NonExistentClass getGameState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final error.NonExistentClass getPlayers() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final error.NonExistentClass getLandlordCards() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.myapplication.ui.viewmodel.GameViewModel.GameUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.Set<com.example.myapplication.data.models.Card>> getSelectedCards() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<java.util.List<com.example.myapplication.data.models.Card>>> getPossiblePlays() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.Integer, java.lang.Integer>> getCallingScores() {
        return null;
    }
    
    /**
     * 开始新游戏
     */
    public final void startNewGame(@org.jetbrains.annotations.NotNull()
    java.lang.String humanPlayerName, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType aiDifficulty1, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType aiDifficulty2) {
    }
    
    /**
     * 叫地主
     */
    public final void callLandlord(int playerIndex, int score) {
    }
    
    /**
     * 出牌
     */
    public final void playCards(int playerIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards) {
    }
    
    /**
     * 过牌
     */
    public final void pass(int playerIndex) {
    }
    
    /**
     * 选择/取消选择牌
     */
    public final void toggleCardSelection(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.Card card) {
    }
    
    /**
     * 清空选中的牌
     */
    public final void clearSelectedCards() {
    }
    
    /**
     * 自动选择牌型
     */
    public final void autoSelectCards(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Card> cards) {
    }
    
    /**
     * 获取选中牌的牌型
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.CardPattern getSelectedPattern() {
        return null;
    }
    
    /**
     * 检查选中的牌是否可以出
     */
    public final boolean canPlaySelectedCards() {
        return false;
    }
    
    /**
     * 重新开始游戏
     */
    public final void restartGame() {
    }
    
    /**
     * 关闭对话框
     */
    public final void dismissDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String dialogType) {
    }
    
    /**
     * 显示设置对话框
     */
    public final void showSettings() {
    }
    
    /**
     * 清除错误消息
     */
    public final void clearError() {
    }
    
    /**
     * 更新UI状态
     */
    private final void updateUiState(com.example.myapplication.data.models.GameState state) {
    }
    
    /**
     * 更新可能的出牌选项
     */
    private final void updatePossiblePlays(java.util.List<com.example.myapplication.data.models.Player> playerList) {
    }
    
    /**
     * 格式化最后出牌信息
     */
    private final java.lang.String formatLastPlayInfo(com.example.myapplication.data.models.GameState state) {
        return null;
    }
    
    /**
     * UI状态数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u001f\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001Bk\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\b\b\u0002\u0010\t\u001a\u00020\b\u0012\b\b\u0002\u0010\n\u001a\u00020\b\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\u0003\u0012\b\b\u0002\u0010\r\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010 \u001a\u00020\bH\u00c6\u0003J\t\u0010!\u001a\u00020\bH\u00c6\u0003J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003Jo\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\b2\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010%\u001a\u00020\u00032\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\'\u001a\u00020(H\u00d6\u0001J\t\u0010)\u001a\u00020\bH\u00d6\u0001R\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0010R\u0011\u0010\n\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0010R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0010R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0010\u00a8\u0006*"}, d2 = {"Lcom/example/myapplication/ui/viewmodel/GameViewModel$GameUiState;", "", "isLoading", "", "showCallingDialog", "showGameOverDialog", "showSettingsDialog", "errorMessage", "", "currentPlayerName", "lastPlayInfo", "canPlay", "canPass", "canCallLandlord", "(ZZZZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZ)V", "getCanCallLandlord", "()Z", "getCanPass", "getCanPlay", "getCurrentPlayerName", "()Ljava/lang/String;", "getErrorMessage", "getLastPlayInfo", "getShowCallingDialog", "getShowGameOverDialog", "getShowSettingsDialog", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class GameUiState {
        private final boolean isLoading = false;
        private final boolean showCallingDialog = false;
        private final boolean showGameOverDialog = false;
        private final boolean showSettingsDialog = false;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String errorMessage = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String currentPlayerName = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String lastPlayInfo = null;
        private final boolean canPlay = false;
        private final boolean canPass = false;
        private final boolean canCallLandlord = false;
        
        public GameUiState(boolean isLoading, boolean showCallingDialog, boolean showGameOverDialog, boolean showSettingsDialog, @org.jetbrains.annotations.Nullable()
        java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
        java.lang.String currentPlayerName, @org.jetbrains.annotations.NotNull()
        java.lang.String lastPlayInfo, boolean canPlay, boolean canPass, boolean canCallLandlord) {
            super();
        }
        
        public final boolean isLoading() {
            return false;
        }
        
        public final boolean getShowCallingDialog() {
            return false;
        }
        
        public final boolean getShowGameOverDialog() {
            return false;
        }
        
        public final boolean getShowSettingsDialog() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getErrorMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getCurrentPlayerName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getLastPlayInfo() {
            return null;
        }
        
        public final boolean getCanPlay() {
            return false;
        }
        
        public final boolean getCanPass() {
            return false;
        }
        
        public final boolean getCanCallLandlord() {
            return false;
        }
        
        public GameUiState() {
            super();
        }
        
        public final boolean component1() {
            return false;
        }
        
        public final boolean component10() {
            return false;
        }
        
        public final boolean component2() {
            return false;
        }
        
        public final boolean component3() {
            return false;
        }
        
        public final boolean component4() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component6() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component7() {
            return null;
        }
        
        public final boolean component8() {
            return false;
        }
        
        public final boolean component9() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.myapplication.ui.viewmodel.GameViewModel.GameUiState copy(boolean isLoading, boolean showCallingDialog, boolean showGameOverDialog, boolean showSettingsDialog, @org.jetbrains.annotations.Nullable()
        java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
        java.lang.String currentPlayerName, @org.jetbrains.annotations.NotNull()
        java.lang.String lastPlayInfo, boolean canPlay, boolean canPass, boolean canCallLandlord) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}