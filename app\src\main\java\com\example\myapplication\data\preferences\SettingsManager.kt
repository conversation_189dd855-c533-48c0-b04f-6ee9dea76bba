package com.example.myapplication.data.preferences

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import com.example.myapplication.data.models.PlayerType
import com.example.myapplication.data.repository.GameSettings
import com.example.myapplication.data.repository.Theme
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map

/**
 * 设置管理器
 * 使用DataStore管理应用设置
 */
class SettingsManager(private val context: Context) {
    
    companion object {
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "game_settings")
        
        // 设置键
        private val PLAYER_NAME = stringPreferencesKey("player_name")
        private val SOUND_ENABLED = booleanPreferencesKey("sound_enabled")
        private val MUSIC_ENABLED = booleanPreferencesKey("music_enabled")
        private val AI_DIFFICULTY_1 = stringPreferencesKey("ai_difficulty_1")
        private val AI_DIFFICULTY_2 = stringPreferencesKey("ai_difficulty_2")
        private val THEME = stringPreferencesKey("theme")
        private val ANIMATION_ENABLED = booleanPreferencesKey("animation_enabled")
        private val AUTO_PLAY = booleanPreferencesKey("auto_play")
        private val GAME_SPEED = intPreferencesKey("game_speed")
        private val VIBRATION_ENABLED = booleanPreferencesKey("vibration_enabled")
        private val CARD_STYLE = stringPreferencesKey("card_style")
        private val BACKGROUND_STYLE = stringPreferencesKey("background_style")
        private val FIRST_LAUNCH = booleanPreferencesKey("first_launch")
        private val TUTORIAL_COMPLETED = booleanPreferencesKey("tutorial_completed")
    }
    
    /**
     * 获取游戏设置流
     */
    val gameSettingsFlow: Flow<GameSettings> = context.dataStore.data.map { preferences ->
        GameSettings(
            playerName = preferences[PLAYER_NAME] ?: "玩家",
            soundEnabled = preferences[SOUND_ENABLED] ?: true,
            musicEnabled = preferences[MUSIC_ENABLED] ?: true,
            aiDifficulty1 = PlayerType.valueOf(preferences[AI_DIFFICULTY_1] ?: PlayerType.AI_MEDIUM.name),
            aiDifficulty2 = PlayerType.valueOf(preferences[AI_DIFFICULTY_2] ?: PlayerType.AI_MEDIUM.name),
            theme = Theme.valueOf(preferences[THEME] ?: Theme.AUTO.name),
            animationEnabled = preferences[ANIMATION_ENABLED] ?: true,
            autoPlay = preferences[AUTO_PLAY] ?: false,
            gameSpeed = preferences[GAME_SPEED] ?: 3
        )
    }
    
    /**
     * 获取玩家名称
     */
    val playerNameFlow: Flow<String> = context.dataStore.data.map { preferences ->
        preferences[PLAYER_NAME] ?: "玩家"
    }
    
    /**
     * 获取音效设置
     */
    val soundEnabledFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[SOUND_ENABLED] ?: true
    }
    
    /**
     * 获取音乐设置
     */
    val musicEnabledFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[MUSIC_ENABLED] ?: true
    }
    
    /**
     * 获取主题设置
     */
    val themeFlow: Flow<Theme> = context.dataStore.data.map { preferences ->
        Theme.valueOf(preferences[THEME] ?: Theme.AUTO.name)
    }
    
    /**
     * 获取动画设置
     */
    val animationEnabledFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[ANIMATION_ENABLED] ?: true
    }
    
    /**
     * 获取震动设置
     */
    val vibrationEnabledFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[VIBRATION_ENABLED] ?: true
    }
    
    /**
     * 获取是否首次启动
     */
    val isFirstLaunchFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[FIRST_LAUNCH] ?: true
    }
    
    /**
     * 获取教程是否完成
     */
    val tutorialCompletedFlow: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[TUTORIAL_COMPLETED] ?: false
    }
    
    /**
     * 保存玩家名称
     */
    suspend fun savePlayerName(name: String) {
        context.dataStore.edit { preferences ->
            preferences[PLAYER_NAME] = name
        }
    }
    
    /**
     * 保存音效设置
     */
    suspend fun saveSoundEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[SOUND_ENABLED] = enabled
        }
    }
    
    /**
     * 保存音乐设置
     */
    suspend fun saveMusicEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[MUSIC_ENABLED] = enabled
        }
    }
    
    /**
     * 保存AI难度设置
     */
    suspend fun saveAIDifficulty(ai1: PlayerType, ai2: PlayerType) {
        context.dataStore.edit { preferences ->
            preferences[AI_DIFFICULTY_1] = ai1.name
            preferences[AI_DIFFICULTY_2] = ai2.name
        }
    }
    
    /**
     * 保存主题设置
     */
    suspend fun saveTheme(theme: Theme) {
        context.dataStore.edit { preferences ->
            preferences[THEME] = theme.name
        }
    }
    
    /**
     * 保存动画设置
     */
    suspend fun saveAnimationEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[ANIMATION_ENABLED] = enabled
        }
    }
    
    /**
     * 保存自动出牌设置
     */
    suspend fun saveAutoPlay(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[AUTO_PLAY] = enabled
        }
    }
    
    /**
     * 保存游戏速度
     */
    suspend fun saveGameSpeed(speed: Int) {
        context.dataStore.edit { preferences ->
            preferences[GAME_SPEED] = speed.coerceIn(1, 5)
        }
    }
    
    /**
     * 保存震动设置
     */
    suspend fun saveVibrationEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[VIBRATION_ENABLED] = enabled
        }
    }
    
    /**
     * 保存卡牌样式
     */
    suspend fun saveCardStyle(style: String) {
        context.dataStore.edit { preferences ->
            preferences[CARD_STYLE] = style
        }
    }
    
    /**
     * 保存背景样式
     */
    suspend fun saveBackgroundStyle(style: String) {
        context.dataStore.edit { preferences ->
            preferences[BACKGROUND_STYLE] = style
        }
    }
    
    /**
     * 标记首次启动完成
     */
    suspend fun markFirstLaunchCompleted() {
        context.dataStore.edit { preferences ->
            preferences[FIRST_LAUNCH] = false
        }
    }
    
    /**
     * 标记教程完成
     */
    suspend fun markTutorialCompleted() {
        context.dataStore.edit { preferences ->
            preferences[TUTORIAL_COMPLETED] = true
        }
    }
    
    /**
     * 保存完整的游戏设置
     */
    suspend fun saveGameSettings(settings: GameSettings) {
        context.dataStore.edit { preferences ->
            preferences[PLAYER_NAME] = settings.playerName
            preferences[SOUND_ENABLED] = settings.soundEnabled
            preferences[MUSIC_ENABLED] = settings.musicEnabled
            preferences[AI_DIFFICULTY_1] = settings.aiDifficulty1.name
            preferences[AI_DIFFICULTY_2] = settings.aiDifficulty2.name
            preferences[THEME] = settings.theme.name
            preferences[ANIMATION_ENABLED] = settings.animationEnabled
            preferences[AUTO_PLAY] = settings.autoPlay
            preferences[GAME_SPEED] = settings.gameSpeed
        }
    }
    
    /**
     * 重置所有设置为默认值
     */
    suspend fun resetToDefaults() {
        context.dataStore.edit { preferences ->
            preferences.clear()
        }
    }
    
    /**
     * 获取当前设置的快照
     */
    suspend fun getCurrentSettings(): GameSettings {
        val preferences = context.dataStore.data.map { it }.first()
        return GameSettings(
            playerName = preferences[PLAYER_NAME] ?: "玩家",
            soundEnabled = preferences[SOUND_ENABLED] ?: true,
            musicEnabled = preferences[MUSIC_ENABLED] ?: true,
            aiDifficulty1 = PlayerType.valueOf(preferences[AI_DIFFICULTY_1] ?: PlayerType.AI_MEDIUM.name),
            aiDifficulty2 = PlayerType.valueOf(preferences[AI_DIFFICULTY_2] ?: PlayerType.AI_MEDIUM.name),
            theme = Theme.valueOf(preferences[THEME] ?: Theme.AUTO.name),
            animationEnabled = preferences[ANIMATION_ENABLED] ?: true,
            autoPlay = preferences[AUTO_PLAY] ?: false,
            gameSpeed = preferences[GAME_SPEED] ?: 3
        )
    }
}
