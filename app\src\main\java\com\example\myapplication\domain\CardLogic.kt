package com.example.myapplication.domain

import com.example.myapplication.data.models.*

/**
 * 扑克牌逻辑处理类
 * 负责牌型识别、牌型比较、洗牌发牌等核心逻辑
 */
class CardLogic {
    
    /**
     * 洗牌算法（Fisher-Yates洗牌）
     */
    fun shuffleDeck(deck: List<Card>): List<Card> {
        val shuffled = deck.toMutableList()
        for (i in shuffled.size - 1 downTo 1) {
            val j = (0..i).random()
            val temp = shuffled[i]
            shuffled[i] = shuffled[j]
            shuffled[j] = temp
        }
        return shuffled
    }
    
    /**
     * 发牌给玩家
     * @param deck 洗好的牌堆
     * @param players 玩家列表
     * @return 剩余的3张底牌
     */
    fun dealCards(deck: List<Card>, players: List<Player>): List<Card> {
        // 清空所有玩家的手牌
        players.forEach { it.clearHand() }
        
        // 每个玩家发17张牌
        for (i in 0 until 17) {
            for (j in players.indices) {
                val cardIndex = i * 3 + j
                if (cardIndex < deck.size - 3) {  // 保留最后3张作为底牌
                    players[j].addCard(deck[cardIndex])
                }
            }
        }
        
        // 返回最后3张底牌
        return deck.takeLast(3)
    }
    
    /**
     * 识别牌型
     */
    fun identifyPattern(cards: List<Card>): CardPattern {
        if (cards.isEmpty()) return CardPattern.invalid()
        
        val sortedCards = cards.sortedBy { it.weight }
        
        return when (cards.size) {
            1 -> CardPattern.single(cards[0])
            2 -> identifyTwoCardPattern(sortedCards)
            3 -> identifyThreeCardPattern(sortedCards)
            4 -> identifyFourCardPattern(sortedCards)
            5 -> identifyFiveCardPattern(sortedCards)
            else -> identifyLongPattern(sortedCards)
        }
    }
    
    /**
     * 识别两张牌的牌型
     */
    private fun identifyTwoCardPattern(cards: List<Card>): CardPattern {
        return when {
            // 王炸
            cards.any { it.rank == Rank.SMALL_JOKER } && 
            cards.any { it.rank == Rank.BIG_JOKER } -> 
                CardPattern.rocket(cards)
            
            // 对子
            cards[0].rank == cards[1].rank -> 
                CardPattern.pair(cards)
            
            else -> CardPattern.invalid()
        }
    }
    
    /**
     * 识别三张牌的牌型
     */
    private fun identifyThreeCardPattern(cards: List<Card>): CardPattern {
        val ranks = cards.map { it.rank }
        val rankCounts = ranks.groupingBy { it }.eachCount()
        
        return when {
            // 三张相同
            rankCounts.size == 1 -> CardPattern.triple(cards)
            else -> CardPattern.invalid()
        }
    }
    
    /**
     * 识别四张牌的牌型
     */
    private fun identifyFourCardPattern(cards: List<Card>): CardPattern {
        val ranks = cards.map { it.rank }
        val rankCounts = ranks.groupingBy { it }.eachCount()
        
        return when {
            // 炸弹
            rankCounts.size == 1 -> CardPattern.bomb(cards)
            
            // 三带一
            rankCounts.size == 2 && rankCounts.values.contains(3) -> {
                val tripleRank = rankCounts.entries.find { it.value == 3 }?.key
                CardPattern(
                    type = CardPatternType.TRIPLE_WITH_SINGLE,
                    cards = cards,
                    mainRank = tripleRank
                )
            }
            
            else -> CardPattern.invalid()
        }
    }
    
    /**
     * 识别五张牌的牌型
     */
    private fun identifyFiveCardPattern(cards: List<Card>): CardPattern {
        val ranks = cards.map { it.rank }
        val rankCounts = ranks.groupingBy { it }.eachCount()
        
        return when {
            // 顺子
            isStraight(ranks) -> CardPattern(
                type = CardPatternType.STRAIGHT,
                cards = cards,
                mainRank = ranks.maxOrNull(),
                length = 5
            )
            
            // 三带二
            rankCounts.size == 2 && rankCounts.values.contains(3) && rankCounts.values.contains(2) -> {
                val tripleRank = rankCounts.entries.find { it.value == 3 }?.key
                CardPattern(
                    type = CardPatternType.TRIPLE_WITH_PAIR,
                    cards = cards,
                    mainRank = tripleRank
                )
            }
            
            else -> CardPattern.invalid()
        }
    }
    
    /**
     * 识别长牌型（6张及以上）
     */
    private fun identifyLongPattern(cards: List<Card>): CardPattern {
        val ranks = cards.map { it.rank }
        val rankCounts = ranks.groupingBy { it }.eachCount()
        
        return when {
            // 顺子（5-12张）
            cards.size in 6..12 && isStraight(ranks) -> CardPattern(
                type = CardPatternType.STRAIGHT,
                cards = cards,
                mainRank = ranks.maxOrNull(),
                length = cards.size
            )
            
            // 连对（6、8、10、12张）
            cards.size % 2 == 0 && isPairStraight(rankCounts) -> CardPattern(
                type = CardPatternType.PAIR_STRAIGHT,
                cards = cards,
                mainRank = rankCounts.keys.maxOrNull(),
                length = cards.size / 2
            )
            
            // 飞机不带翼
            cards.size % 3 == 0 && isTripleStraight(rankCounts) -> CardPattern(
                type = CardPatternType.TRIPLE_STRAIGHT,
                cards = cards,
                mainRank = rankCounts.keys.maxOrNull(),
                length = cards.size / 3
            )
            
            // 四带二单
            cards.size == 6 && isFourWithTwoSingles(rankCounts) -> {
                val fourRank = rankCounts.entries.find { it.value == 4 }?.key
                CardPattern(
                    type = CardPatternType.FOUR_WITH_TWO_SINGLES,
                    cards = cards,
                    mainRank = fourRank
                )
            }
            
            // 四带二对
            cards.size == 8 && isFourWithTwoPairs(rankCounts) -> {
                val fourRank = rankCounts.entries.find { it.value == 4 }?.key
                CardPattern(
                    type = CardPatternType.FOUR_WITH_TWO_PAIRS,
                    cards = cards,
                    mainRank = fourRank
                )
            }
            
            else -> identifyComplexPattern(cards, rankCounts)
        }
    }
    
    /**
     * 识别复杂牌型（飞机带翼等）
     */
    private fun identifyComplexPattern(cards: List<Card>, rankCounts: Map<Rank, Int>): CardPattern {
        val triples = rankCounts.filter { it.value == 3 }.keys.sorted()
        val pairs = rankCounts.filter { it.value == 2 }.keys.sorted()
        val singles = rankCounts.filter { it.value == 1 }.keys.sorted()
        
        // 飞机带单牌
        if (triples.size >= 2 && isConsecutive(triples) && 
            singles.size == triples.size && pairs.isEmpty()) {
            return CardPattern(
                type = CardPatternType.TRIPLE_STRAIGHT_WITH_SINGLES,
                cards = cards,
                mainRank = triples.maxOrNull(),
                length = triples.size
            )
        }
        
        // 飞机带对子
        if (triples.size >= 2 && isConsecutive(triples) && 
            pairs.size == triples.size && singles.isEmpty()) {
            return CardPattern(
                type = CardPatternType.TRIPLE_STRAIGHT_WITH_PAIRS,
                cards = cards,
                mainRank = triples.maxOrNull(),
                length = triples.size
            )
        }
        
        return CardPattern.invalid()
    }
    
    /**
     * 判断是否为顺子
     */
    private fun isStraight(ranks: List<Rank>): Boolean {
        if (ranks.size < 5) return false
        if (ranks.any { it.value >= 15 }) return false  // 2和王不能组成顺子
        
        val uniqueRanks = ranks.distinct().sorted()
        if (uniqueRanks.size != ranks.size) return false
        
        return isConsecutive(uniqueRanks)
    }
    
    /**
     * 判断是否为连对
     */
    private fun isPairStraight(rankCounts: Map<Rank, Int>): Boolean {
        if (rankCounts.values.any { it != 2 }) return false
        if (rankCounts.keys.any { it.value >= 15 }) return false
        
        return isConsecutive(rankCounts.keys.sorted())
    }
    
    /**
     * 判断是否为飞机（三张连续）
     */
    private fun isTripleStraight(rankCounts: Map<Rank, Int>): Boolean {
        val triples = rankCounts.filter { it.value == 3 }.keys
        if (triples.size < 2) return false
        if (triples.any { it.value >= 15 }) return false
        
        return isConsecutive(triples.sorted())
    }
    
    /**
     * 判断是否为四带二单
     */
    private fun isFourWithTwoSingles(rankCounts: Map<Rank, Int>): Boolean {
        return rankCounts.size == 3 && 
               rankCounts.values.count { it == 4 } == 1 &&
               rankCounts.values.count { it == 1 } == 2
    }
    
    /**
     * 判断是否为四带二对
     */
    private fun isFourWithTwoPairs(rankCounts: Map<Rank, Int>): Boolean {
        return rankCounts.size == 3 && 
               rankCounts.values.count { it == 4 } == 1 &&
               rankCounts.values.count { it == 2 } == 2
    }
    
    /**
     * 判断点数是否连续
     */
    private fun isConsecutive(ranks: List<Rank>): Boolean {
        if (ranks.size < 2) return false
        
        for (i in 1 until ranks.size) {
            if (ranks[i].value != ranks[i-1].value + 1) {
                return false
            }
        }
        return true
    }
    
    /**
     * 获取可以压过指定牌型的所有可能出牌
     */
    fun getPossiblePlays(hand: List<Card>, targetPattern: CardPattern?): List<List<Card>> {
        val possiblePlays = mutableListOf<List<Card>>()

        if (targetPattern == null) {
            // 如果没有目标牌型，返回所有可能的出牌
            possiblePlays.addAll(getAllPossiblePlays(hand))
        } else {
            // 寻找可以压过目标牌型的出牌
            possiblePlays.addAll(getBeatingPlays(hand, targetPattern))
        }

        // 按牌的权重排序，优先推荐小牌
        return possiblePlays.sortedBy { cards ->
            cards.sumOf { it.weight }
        }
    }
    
    /**
     * 获取所有可能的出牌组合
     */
    private fun getAllPossiblePlays(hand: List<Card>): List<List<Card>> {
        val plays = mutableListOf<List<Card>>()

        // 单牌
        hand.forEach { card ->
            plays.add(listOf(card))
        }

        val rankGroups = hand.groupBy { it.rank }

        // 对子、三张、炸弹
        rankGroups.forEach { (_, cards) ->
            if (cards.size >= 2) plays.add(cards.take(2))
            if (cards.size >= 3) plays.add(cards.take(3))
            if (cards.size == 4) plays.add(cards)
        }

        // 三带一、三带二
        val triples = rankGroups.filter { it.value.size >= 3 }
        val singles = rankGroups.filter { it.value.size == 1 }
        val pairs = rankGroups.filter { it.value.size == 2 }

        triples.forEach { (tripleRank, tripleCards) ->
            // 三带一
            singles.forEach { (singleRank, singleCards) ->
                if (singleRank != tripleRank) {
                    plays.add(tripleCards.take(3) + singleCards.take(1))
                }
            }
            // 三带二
            pairs.forEach { (pairRank, pairCards) ->
                if (pairRank != tripleRank) {
                    plays.add(tripleCards.take(3) + pairCards.take(2))
                }
            }
        }

        // 王炸
        val smallJoker = hand.find { it.rank == Rank.SMALL_JOKER }
        val bigJoker = hand.find { it.rank == Rank.BIG_JOKER }
        if (smallJoker != null && bigJoker != null) {
            plays.add(listOf(smallJoker, bigJoker))
        }

        // 顺子（5张及以上连续单牌）
        plays.addAll(findStraights(hand))

        // 连对（3对及以上连续对子）
        plays.addAll(findPairStraights(hand))

        return plays.filter { identifyPattern(it).isValid }
    }
    
    /**
     * 寻找顺子
     */
    private fun findStraights(hand: List<Card>): List<List<Card>> {
        val straights = mutableListOf<List<Card>>()
        val normalCards = hand.filter { !it.isJoker && it.rank.value < 15 } // 排除2和王
        val sortedCards = normalCards.sortedBy { it.rank.value }

        for (length in 5..12) { // 顺子长度5-12
            for (i in 0..sortedCards.size - length) {
                val potential = sortedCards.subList(i, i + length)
                if (isConsecutive(potential.map { it.rank })) {
                    straights.add(potential)
                }
            }
        }

        return straights
    }

    /**
     * 寻找连对
     */
    private fun findPairStraights(hand: List<Card>): List<List<Card>> {
        val pairStraights = mutableListOf<List<Card>>()
        val rankGroups = hand.groupBy { it.rank }
        val pairs = rankGroups.filter { it.value.size >= 2 && it.key.value < 15 }

        val sortedPairs = pairs.toList().sortedBy { it.first.value }

        for (length in 3..6) { // 连对长度3-6
            for (i in 0..sortedPairs.size - length) {
                val potentialPairs = sortedPairs.subList(i, i + length)
                val ranks = potentialPairs.map { it.first }
                if (isConsecutive(ranks)) {
                    val cards = potentialPairs.flatMap { it.second.take(2) }
                    pairStraights.add(cards)
                }
            }
        }

        return pairStraights
    }

    /**
     * 检查牌是否连续
     */
    private fun isConsecutive(ranks: List<Rank>): Boolean {
        if (ranks.size < 2) return false
        val values = ranks.map { it.value }.sorted()
        for (i in 1 until values.size) {
            if (values[i] != values[i-1] + 1) return false
        }
        return true
    }

    /**
     * 获取可以压过目标牌型的出牌
     */
    private fun getBeatingPlays(hand: List<Card>, targetPattern: CardPattern): List<List<Card>> {
        val beatingPlays = mutableListOf<List<Card>>()
        val allPlays = getAllPossiblePlays(hand)

        allPlays.forEach { play ->
            val pattern = identifyPattern(play)
            if (pattern.canBeat(targetPattern)) {
                beatingPlays.add(play)
            }
        }

        return beatingPlays
    }
}
