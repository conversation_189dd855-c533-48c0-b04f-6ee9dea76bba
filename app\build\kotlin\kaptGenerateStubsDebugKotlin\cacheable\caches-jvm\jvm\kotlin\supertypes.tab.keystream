&com.example.myapplication.MainActivity4com.example.myapplication.data.database.GameDatabase*com.example.myapplication.data.models.Suit/com.example.myapplication.data.models.CardColor*com.example.myapplication.data.models.Rank*com.example.myapplication.data.models.Card5com.example.myapplication.data.models.CardPatternType/com.example.myapplication.data.models.GamePhase0com.example.myapplication.data.models.GameResult/com.example.myapplication.data.models.GameState0com.example.myapplication.data.models.PlayerType0com.example.myapplication.data.models.PlayerRole4com.example.myapplication.data.models.PlayerPosition,com.example.myapplication.data.models.Player/com.example.myapplication.data.repository.ThemeDcom.example.myapplication.ui.animation.GameAnimations.SlideDirection0com.example.myapplication.ui.components.CardSize9com.example.myapplication.ui.sound.SoundManager.SoundType=com.example.myapplication.ui.sound.SoundManager.VibrationType4com.example.myapplication.ui.viewmodel.GameViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     