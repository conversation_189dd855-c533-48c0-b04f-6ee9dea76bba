@echo off
echo ========================================
echo SSL修复测试脚本
echo ========================================

echo.
echo 🔍 测试网络连接...
ping -n 1 google.com >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ 基本网络连接正常
) else (
    echo ❌ 基本网络连接失败
)

echo.
echo 🔍 测试Maven仓库连接...
ping -n 1 repo1.maven.org >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Maven仓库可达
) else (
    echo ❌ Maven仓库不可达
)

echo.
echo 🔍 测试Google Maven仓库...
ping -n 1 maven.google.com >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Google Maven仓库可达
) else (
    echo ❌ Google Maven仓库不可达
)

echo.
echo 🔧 设置SSL禁用环境变量...
set GRADLE_OPTS=-Djavax.net.ssl.trustStore=NONE -Dcom.sun.net.ssl.checkRevocation=false -Dsun.security.ssl.allowUnsafeRenegotiation=true

echo.
echo 🧪 测试Gradle连接（禁用SSL）...
call gradlew --version --init-script gradle/init.gradle

if %ERRORLEVEL% EQU 0 (
    echo ✅ Gradle SSL修复成功
    echo.
    echo 🚀 现在可以尝试构建项目：
    echo    build-no-ssl.bat
) else (
    echo ❌ Gradle SSL修复失败
    echo.
    echo 💡 建议尝试：
    echo    1. 检查防火墙设置
    echo    2. 使用VPN或代理
    echo    3. 联系网络管理员
)

echo.
echo ========================================
echo 测试完成
echo ========================================

pause
