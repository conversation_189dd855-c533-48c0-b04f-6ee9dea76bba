package com.example.myapplication.data.repository;

/**
 * 游戏设置数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u001e\b\u0087\b\u0018\u00002\u00020\u0001BM\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\b\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\u0005\u0012\u0006\u0010\r\u001a\u00020\u0005\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\"\u001a\u00020\bH\u00c6\u0003J\t\u0010#\u001a\u00020\bH\u00c6\u0003J\t\u0010$\u001a\u00020\u000bH\u00c6\u0003J\t\u0010%\u001a\u00020\u0005H\u00c6\u0003J\t\u0010&\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\'\u001a\u00020\u000fH\u00c6\u0003Jc\u0010(\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\r\u001a\u00020\u00052\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u00c6\u0001J\u0013\u0010)\u001a\u00020\u00052\b\u0010*\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010+\u001a\u00020\u000fH\u00d6\u0001J\t\u0010,\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\r\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0015R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001e\u00a8\u0006-"}, d2 = {"Lcom/example/myapplication/data/repository/GameSettings;", "", "playerName", "", "soundEnabled", "", "musicEnabled", "aiDifficulty1", "Lcom/example/myapplication/data/models/PlayerType;", "aiDifficulty2", "theme", "Lcom/example/myapplication/data/repository/Theme;", "animationEnabled", "autoPlay", "gameSpeed", "", "(Ljava/lang/String;ZZLcom/example/myapplication/data/models/PlayerType;Lcom/example/myapplication/data/models/PlayerType;Lcom/example/myapplication/data/repository/Theme;ZZI)V", "getAiDifficulty1", "()Lcom/example/myapplication/data/models/PlayerType;", "getAiDifficulty2", "getAnimationEnabled", "()Z", "getAutoPlay", "getGameSpeed", "()I", "getMusicEnabled", "getPlayerName", "()Ljava/lang/String;", "getSoundEnabled", "getTheme", "()Lcom/example/myapplication/data/repository/Theme;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
public final class GameSettings {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String playerName = null;
    private final boolean soundEnabled = false;
    private final boolean musicEnabled = false;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.models.PlayerType aiDifficulty1 = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.models.PlayerType aiDifficulty2 = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.repository.Theme theme = null;
    private final boolean animationEnabled = false;
    private final boolean autoPlay = false;
    private final int gameSpeed = 0;
    
    public GameSettings(@org.jetbrains.annotations.NotNull()
    java.lang.String playerName, boolean soundEnabled, boolean musicEnabled, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType aiDifficulty1, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType aiDifficulty2, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.repository.Theme theme, boolean animationEnabled, boolean autoPlay, int gameSpeed) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPlayerName() {
        return null;
    }
    
    public final boolean getSoundEnabled() {
        return false;
    }
    
    public final boolean getMusicEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.PlayerType getAiDifficulty1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.PlayerType getAiDifficulty2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.repository.Theme getTheme() {
        return null;
    }
    
    public final boolean getAnimationEnabled() {
        return false;
    }
    
    public final boolean getAutoPlay() {
        return false;
    }
    
    public final int getGameSpeed() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.PlayerType component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.models.PlayerType component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.repository.Theme component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.myapplication.data.repository.GameSettings copy(@org.jetbrains.annotations.NotNull()
    java.lang.String playerName, boolean soundEnabled, boolean musicEnabled, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType aiDifficulty1, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType aiDifficulty2, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.repository.Theme theme, boolean animationEnabled, boolean autoPlay, int gameSpeed) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}