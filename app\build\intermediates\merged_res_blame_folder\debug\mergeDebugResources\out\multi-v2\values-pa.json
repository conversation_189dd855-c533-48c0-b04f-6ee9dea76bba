{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-61:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6dce069ed5f76821f2d9938d1900d2a1\\transformed\\core-1.16.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,558,656,785", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "148,250,350,451,553,651,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,80", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,506,608,706,8167", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "198,300,400,501,603,701,830,8263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\965fb6c3bf340bea42b1b1c81d253a97\\transformed\\foundation-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "84,85", "startColumns": "4,4", "startOffsets": "8532,8619", "endColumns": "86,86", "endOffsets": "8614,8701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3bdae484d8629b91b71d2a8e711d605e\\transformed\\material3-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,291,407,526,623,724,842,980,1104,1247,1332,1435,1525,1622,1734,1855,1963,2098,2235,2366,2532,2658,2773,2892,3012,3103,3199,3318,3454,3556,3659,3765,3897,4035,4146,4245,4321,4418,4519,4604,4692,4791,4871,4955,5055,5154,5249,5347,5433,5534,5632,5734,5849,5929,6031", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "167,286,402,521,618,719,837,975,1099,1242,1327,1430,1520,1617,1729,1850,1958,2093,2230,2361,2527,2653,2768,2887,3007,3098,3194,3313,3449,3551,3654,3760,3892,4030,4141,4240,4316,4413,4514,4599,4687,4786,4866,4950,5050,5149,5244,5342,5428,5529,5627,5729,5844,5924,6026,6122"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1466,1583,1702,1818,1937,2034,2135,2253,2391,2515,2658,2743,2846,2936,3033,3145,3266,3374,3509,3646,3777,3943,4069,4184,4303,4423,4514,4610,4729,4865,4967,5070,5176,5308,5446,5557,5656,5732,5829,5930,6015,6103,6202,6282,6366,6466,6565,6660,6758,6844,6945,7043,7145,7260,7340,7442", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "1578,1697,1813,1932,2029,2130,2248,2386,2510,2653,2738,2841,2931,3028,3140,3261,3369,3504,3641,3772,3938,4064,4179,4298,4418,4509,4605,4724,4860,4962,5065,5171,5303,5441,5552,5651,5727,5824,5925,6010,6098,6197,6277,6361,6461,6560,6655,6753,6839,6940,7038,7140,7255,7335,7437,7533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f11933d2f9c817336fdb6ff83a5031f8\\transformed\\ui-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,987,1068,1140,1215,1290,1365,1445,1511", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,74,74,74,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,982,1063,1135,1210,1285,1360,1440,1506,1624"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,79,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,928,1011,1103,1202,1290,1368,7538,7626,7710,7789,7870,7942,8017,8092,8268,8348,8414", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,74,74,74,79,65,117", "endOffsets": "923,1006,1098,1197,1285,1363,1461,7621,7705,7784,7865,7937,8012,8087,8162,8343,8409,8527"}}]}]}