package com.example.myapplication.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.myapplication.data.models.*
import com.example.myapplication.ui.viewmodel.GameViewModel

/**
 * 游戏状态指示器
 */
@Composable
fun GameStatusIndicator(
    currentPhase: GamePhase,
    currentPlayerName: String,
    timeRemaining: Int? = null,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(8.dp),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = when (currentPhase) {
                GamePhase.CALLING -> MaterialTheme.colorScheme.primaryContainer
                GamePhase.PLAYING -> MaterialTheme.colorScheme.secondaryContainer
                GamePhase.FINISHED -> MaterialTheme.colorScheme.tertiaryContainer
                else -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 状态图标
            Icon(
                imageVector = when (currentPhase) {
                    GamePhase.CALLING -> Icons.Default.RecordVoiceOver
                    GamePhase.PLAYING -> Icons.Default.PlayArrow
                    GamePhase.FINISHED -> Icons.Default.EmojiEvents
                    else -> Icons.Default.HourglassEmpty
                },
                contentDescription = null,
                modifier = Modifier.size(20.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.width(8.dp))

            Column {
                Text(
                    text = when (currentPhase) {
                        GamePhase.CALLING -> "叫地主阶段"
                        GamePhase.PLAYING -> "出牌阶段"
                        GamePhase.FINISHED -> "游戏结束"
                        else -> "准备中"
                    },
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Text(
                    text = "当前: $currentPlayerName",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 倒计时（如果有）
            timeRemaining?.let { time ->
                Spacer(modifier = Modifier.weight(1f))
                Text(
                    text = "${time}s",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = if (time <= 10) Color.Red else MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 中央出牌区域
 */
@Composable
fun CentralPlayArea(
    lastPlayedCards: List<Card>,
    lastPlayerIndex: Int,
    players: List<Player>
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        if (lastPlayedCards.isNotEmpty()) {
            Card(
                modifier = Modifier.padding(16.dp),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 出牌玩家名称
                    val playerName = players.getOrNull(lastPlayerIndex)?.name ?: "未知玩家"
                    Text(
                        text = "$playerName 出牌",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 出的牌
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy((-12).dp)
                    ) {
                        items(lastPlayedCards) { card ->
                            CardComponent(
                                card = card,
                                size = CardSize.NORMAL,
                                isEnabled = false
                            )
                        }
                    }
                }
            }
        } else {
            // 等待出牌提示
            Card(
                modifier = Modifier.padding(16.dp),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)
                )
            ) {
                Box(
                    modifier = Modifier.padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "等待出牌",
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

/**
 * 叫地主对话框
 */
@Composable
fun CallingDialog(
    gameViewModel: GameViewModel,
    onDismiss: () -> Unit
) {
    val players by gameViewModel.players.collectAsStateWithLifecycle()
    val gameState by gameViewModel.gameState.collectAsStateWithLifecycle()
    val callingScores by gameViewModel.callingScores.collectAsStateWithLifecycle()
    
    val humanPlayer = players.find { it.isHuman }
    val humanIndex = players.indexOfFirst { it.isHuman }
    val isHumanTurn = gameState.currentPlayerIndex == humanIndex
    val hasHumanCalled = humanPlayer?.hasCalledLandlord ?: true
    
    Dialog(onDismissRequest = { /* 不允许点击外部关闭 */ }) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "叫地主",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 显示当前叫地主情况
                callingScores.forEach { (playerIndex, score) ->
                    val player = players.getOrNull(playerIndex)
                    if (player != null) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = player.name,
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            Text(
                                text = "${score}分",
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.primary,
                                fontWeight = FontWeight.Medium
                            )
                        }
                        Spacer(modifier = Modifier.height(4.dp))
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                if (isHumanTurn && !hasHumanCalled) {
                    Text(
                        text = "请选择叫地主分数",
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 叫地主按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        listOf(1, 2, 3).forEach { score ->
                            Button(
                                onClick = {
                                    gameViewModel.callLandlord(humanIndex, score)
                                },
                                enabled = score > (callingScores.values.maxOrNull() ?: 0),
                                modifier = Modifier.weight(1f)
                            ) {
                                Text("${score}分")
                            }
                            if (score < 3) {
                                Spacer(modifier = Modifier.width(8.dp))
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 不叫按钮
                    OutlinedButton(
                        onClick = {
                            gameViewModel.callLandlord(humanIndex, 0)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("不叫")
                    }
                } else {
                    val currentPlayer = players.getOrNull(gameState.currentPlayerIndex)
                    Text(
                        text = if (hasHumanCalled) {
                            "等待其他玩家叫地主..."
                        } else {
                            "等待 ${currentPlayer?.name ?: "未知"} 叫地主..."
                        },
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    CircularProgressIndicator(
                        modifier = Modifier.size(32.dp),
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

/**
 * 游戏结束对话框
 */
@Composable
fun GameOverDialog(
    gameState: GameState,
    players: List<Player>,
    onRestart: () -> Unit,
    onDismiss: () -> Unit
) {
    val winner = players.getOrNull(gameState.winnerIndex)
    val isLandlordWin = gameState.result == GameResult.LANDLORD_WIN
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 胜利图标
                Icon(
                    imageVector = if (winner?.isHuman == true) Icons.Default.EmojiEvents else Icons.Default.SentimentDissatisfied,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = if (winner?.isHuman == true) Color(0xFFFFD700) else Color.Gray
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 游戏结果
                Text(
                    text = if (isLandlordWin) "地主胜利！" else "农民胜利！",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                winner?.let {
                    Text(
                        text = "获胜者: ${it.name}",
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 游戏统计
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "游戏统计",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        players.forEach { player ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "${player.name} (${if (player.isLandlord) "地主" else "农民"})",
                                    fontSize = 14.sp,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                                Text(
                                    text = "${if (player.score > 0) "+" else ""}${player.score}",
                                    fontSize = 14.sp,
                                    color = if (player.score > 0) Color.Green else Color.Red,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                            Spacer(modifier = Modifier.height(4.dp))
                        }
                        
                        if (gameState.bombCount > 0) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "炸弹数量: ${gameState.bombCount}",
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                        }
                        
                        if (gameState.isSpring) {
                            Text(
                                text = "春天！",
                                fontSize = 14.sp,
                                color = Color.Red,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("返回主页")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = onRestart,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("再来一局")
                    }
                }
            }
        }
    }
}

/**
 * 牌型提示组件
 */
@Composable
fun CardPatternHint(
    selectedCards: List<Card>,
    pattern: CardPattern?,
    modifier: Modifier = Modifier
) {
    if (selectedCards.isNotEmpty()) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = if (pattern?.isValid == true) {
                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.6f)
                } else {
                    MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.6f)
                }
            )
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (pattern?.isValid == true) {
                        Icons.Default.CheckCircle
                    } else {
                        Icons.Default.Error
                    },
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = if (pattern?.isValid == true) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onErrorContainer
                    }
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = if (pattern?.isValid == true) {
                        "牌型: ${pattern.type.displayName}"
                    } else {
                        "无效牌型"
                    },
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (pattern?.isValid == true) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onErrorContainer
                    }
                )

                if (pattern?.isValid == true && pattern.isBomb) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "💥",
                        fontSize = 16.sp
                    )
                }
            }
        }
    }
}
