# 斗地主游戏 - 构建说明

## 🚀 快速开始

### 环境要求
- **Android Studio**: Flamingo (2022.2.1) 或更高版本
- **JDK**: 8 或更高版本（推荐JDK 11）
- **Android SDK**: API 24+ (Android 7.0)
- **Gradle**: 7.5+

### 构建步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd MyApplication2
   ```

2. **打开Android Studio**
   - 选择 "Open an existing project"
   - 选择项目根目录

3. **等待Gradle同步**
   - Android Studio会自动开始Gradle同步
   - 如果遇到问题，点击 "Sync Project with Gradle Files"

4. **解决可能的构建问题**

### 🔧 常见构建问题及解决方案

#### 1. Java版本问题
如果遇到 "Java 8 JVM" 错误：

**解决方案A**: 更新项目JDK设置
- File → Project Structure → SDK Location
- 确保使用JDK 8或更高版本（推荐JDK 11）

**解决方案B**: 使用兼容版本（已在项目中配置）
```kotlin
// gradle/libs.versions.toml
agp = "7.4.2"  // 兼容JDK 8
kotlin = "1.8.10"  // 稳定版本
```

#### 2. Kotlin编译问题
如果遇到Kotlin编译错误：

**检查Kotlin版本兼容性**
```kotlin
// app/build.gradle.kts
composeOptions {
    kotlinCompilerExtensionVersion = "1.4.3"  // 匹配Kotlin 1.8.10
}
```

#### 3. 依赖冲突
如果遇到依赖解析问题：

**清理并重建**
```bash
./gradlew clean
./gradlew build
```

#### 4. Room数据库编译问题
确保kapt插件正确配置：
```kotlin
plugins {
    id("kotlin-kapt")
}
```

### 📱 运行应用

1. **连接设备或启动模拟器**
   - 物理设备：启用开发者选项和USB调试
   - 模拟器：创建API 24+的虚拟设备

2. **运行应用**
   - 点击绿色的"Run"按钮
   - 或使用快捷键 Shift+F10

### 🧪 运行测试

```bash
# 单元测试
./gradlew test

# 仪器测试（需要连接设备）
./gradlew connectedAndroidTest
```

### 🔍 调试构建问题

#### 启用详细日志
```bash
./gradlew build --info --stacktrace
```

#### 检查依赖树
```bash
./gradlew app:dependencies
```

#### 清理缓存
```bash
./gradlew clean
# 或在Android Studio中: Build → Clean Project
```

### 📦 生成APK

#### Debug版本
```bash
./gradlew assembleDebug
```
APK位置: `app/build/outputs/apk/debug/`

#### Release版本
```bash
./gradlew assembleRelease
```
APK位置: `app/build/outputs/apk/release/`

### 🎯 项目特性验证

构建成功后，您可以验证以下功能：

1. **启动应用** - 显示欢迎界面
2. **开始游戏** - 进入叫地主阶段
3. **AI交互** - AI自动叫地主和出牌
4. **手牌操作** - 选择和出牌功能
5. **游戏流程** - 完整的游戏循环

### 🛠️ 开发环境配置

#### 推荐的Android Studio插件
- Kotlin
- Android APK Analyzer
- Database Inspector

#### 代码风格
项目使用标准的Kotlin代码风格，建议启用：
- File → Settings → Editor → Code Style → Kotlin
- 选择 "Use default style"

### 📋 故障排除清单

如果构建失败，请按顺序检查：

- [ ] JDK版本是否为11+
- [ ] Android SDK是否正确安装
- [ ] 网络连接是否正常（下载依赖）
- [ ] 磁盘空间是否充足
- [ ] 防火墙是否阻止Gradle下载
- [ ] 是否有其他Android Studio实例运行

### 🔄 重置项目状态

如果遇到无法解决的问题：

1. **清理所有缓存**
   ```bash
   ./gradlew clean
   rm -rf .gradle
   rm -rf build
   rm -rf app/build
   ```

2. **重新导入项目**
   - 关闭Android Studio
   - 删除 `.idea` 文件夹
   - 重新打开项目

3. **重置Gradle**
   - File → Invalidate Caches and Restart

### 📞 获取帮助

如果仍然遇到问题：

1. 检查Android Studio的 "Build" 窗口中的详细错误信息
2. 查看 "Event Log" 中的警告和错误
3. 在GitHub Issues中搜索类似问题
4. 提供完整的错误日志和环境信息

### 🎮 享受游戏！

构建成功后，您就可以体验这个功能完整的斗地主游戏了！

---

**注意**: 这是一个演示项目，音效文件使用了占位符。在生产环境中，请添加真实的音效文件到 `app/src/main/res/raw/` 目录。
