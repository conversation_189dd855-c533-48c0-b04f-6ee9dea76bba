@echo off
echo ========================================
echo 斗地主游戏离线构建脚本
echo ========================================

echo.
echo 1. 清理项目...
call gradlew clean

echo.
echo 2. 检查依赖...
call gradlew dependencies --configuration implementation

echo.
echo 3. 构建Debug版本（禁用SSL验证）...
call gradlew assembleDebug --init-script gradle/init.gradle --offline

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo 离线构建失败，尝试在线构建（禁用SSL验证）...
    call gradlew assembleDebug --init-script gradle/init.gradle

    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo 在线构建也失败，尝试使用HTTP仓库...
        call gradlew assembleDebug --init-script gradle/init.gradle -Dorg.gradle.internal.http.connectionTimeout=120000 -Dorg.gradle.internal.http.socketTimeout=120000
    )
)

echo.
echo 4. 运行测试...
call gradlew test

echo.
echo ========================================
echo 构建完成！
echo APK位置: app\build\outputs\apk\debug\
echo ========================================

pause
