@echo off
echo ========================================
echo 斗地主游戏离线构建脚本
echo ========================================

echo.
echo 1. 清理项目...
call gradlew clean

echo.
echo 2. 检查依赖...
call gradlew dependencies --configuration implementation

echo.
echo 3. 构建Debug版本...
call gradlew assembleDebug --offline

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo 离线构建失败，尝试在线构建...
    call gradlew assembleDebug
)

echo.
echo 4. 运行测试...
call gradlew test

echo.
echo ========================================
echo 构建完成！
echo APK位置: app\build\outputs\apk\debug\
echo ========================================

pause
