package com.example.myapplication.ui.theme;

/**
 * 主题管理器
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0004\u0019\u001a\u001b\u001cB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001b\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0007J\b\u0010\r\u001a\u00020\nH\u0002J\b\u0010\u000e\u001a\u00020\nH\u0002J\u001b\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u0011\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0012\u0010\u0013J\u001d\u0010\u0014\u001a\u00020\u00042\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0017\u0010\u0018\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u001d"}, d2 = {"Lcom/example/myapplication/ui/theme/ThemeManager;", "", "()V", "getCardColor", "Landroidx/compose/ui/graphics/Color;", "card", "Lcom/example/myapplication/data/models/Card;", "getCardColor-vNxB06k", "(Lcom/example/myapplication/data/models/Card;)J", "getColorScheme", "Landroidx/compose/material3/ColorScheme;", "theme", "Lcom/example/myapplication/data/repository/Theme;", "getDarkColorScheme", "getLightColorScheme", "getPatternColor", "pattern", "Lcom/example/myapplication/data/models/CardPattern;", "getPatternColor-vNxB06k", "(Lcom/example/myapplication/data/models/CardPattern;)J", "getPlayerRoleColor", "role", "Lcom/example/myapplication/data/models/PlayerRole;", "getPlayerRoleColor-vNxB06k", "(Lcom/example/myapplication/data/models/PlayerRole;)J", "AnimationDurations", "GameColors", "GameDimensions", "GameTypography", "app_debug"})
public final class ThemeManager {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.myapplication.ui.theme.ThemeManager INSTANCE = null;
    
    private ThemeManager() {
        super();
    }
    
    /**
     * 获取当前主题的颜色方案
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.material3.ColorScheme getColorScheme(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.repository.Theme theme) {
        return null;
    }
    
    /**
     * 浅色主题颜色方案
     */
    private final androidx.compose.material3.ColorScheme getLightColorScheme() {
        return null;
    }
    
    /**
     * 深色主题颜色方案
     */
    private final androidx.compose.material3.ColorScheme getDarkColorScheme() {
        return null;
    }
    
    /**
     * 动画持续时间
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\b\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/example/myapplication/ui/theme/ThemeManager$AnimationDurations;", "", "()V", "bombExplosion", "", "cardFlip", "cardPlay", "dealingCard", "fadeInOut", "scaleInOut", "shuffleCards", "slideInOut", "app_debug"})
    public static final class AnimationDurations {
        public static final int cardFlip = 600;
        public static final int cardPlay = 300;
        public static final int bombExplosion = 500;
        public static final int fadeInOut = 300;
        public static final int slideInOut = 400;
        public static final int scaleInOut = 250;
        public static final int dealingCard = 200;
        public static final int shuffleCards = 1000;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.myapplication.ui.theme.ThemeManager.AnimationDurations INSTANCE = null;
        
        private AnimationDurations() {
            super();
        }
    }
    
    /**
     * 游戏专用颜色
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b \b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0019\u0010\u0003\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006R\u0019\u0010\b\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\t\u0010\u0006R\u0019\u0010\n\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000b\u0010\u0006R\u0019\u0010\f\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\r\u0010\u0006R\u0019\u0010\u000e\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000f\u0010\u0006R\u0019\u0010\u0010\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0011\u0010\u0006R\u0019\u0010\u0012\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0013\u0010\u0006R\u0019\u0010\u0014\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0015\u0010\u0006R\u0019\u0010\u0016\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0017\u0010\u0006R\u0019\u0010\u0018\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0019\u0010\u0006R\u0019\u0010\u001a\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001b\u0010\u0006R\u0019\u0010\u001c\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001d\u0010\u0006R\u0019\u0010\u001e\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001f\u0010\u0006R\u0019\u0010 \u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b!\u0010\u0006R\u0019\u0010\"\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b#\u0010\u0006\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006$"}, d2 = {"Lcom/example/myapplication/ui/theme/ThemeManager$GameColors;", "", "()V", "blackSuit", "Landroidx/compose/ui/graphics/Color;", "getBlackSuit-0d7_KjU", "()J", "J", "bombColor", "getBombColor-0d7_KjU", "cardBackground", "getCardBackground-0d7_KjU", "cardBorder", "getCardBorder-0d7_KjU", "cardSelected", "getCardSelected-0d7_KjU", "farmerColor", "getFarmerColor-0d7_KjU", "jokerColor", "getJokerColor-0d7_KjU", "landlordColor", "getLandlordColor-0d7_KjU", "loseColor", "getLoseColor-0d7_KjU", "redSuit", "getRedSuit-0d7_KjU", "rocketColor", "getRocketColor-0d7_KjU", "tableDark", "getTableDark-0d7_KjU", "tableGreen", "getTableGreen-0d7_KjU", "warningColor", "getWarningColor-0d7_KjU", "winColor", "getWinColor-0d7_KjU", "app_debug"})
    public static final class GameColors {
        private static final long cardBackground = 0L;
        private static final long cardBorder = 0L;
        private static final long cardSelected = 0L;
        private static final long redSuit = 0L;
        private static final long blackSuit = 0L;
        private static final long jokerColor = 0L;
        private static final long tableGreen = 0L;
        private static final long tableDark = 0L;
        private static final long landlordColor = 0L;
        private static final long farmerColor = 0L;
        private static final long winColor = 0L;
        private static final long loseColor = 0L;
        private static final long warningColor = 0L;
        private static final long bombColor = 0L;
        private static final long rocketColor = 0L;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.myapplication.ui.theme.ThemeManager.GameColors INSTANCE = null;
        
        private GameColors() {
            super();
        }
    }
    
    /**
     * 游戏专用尺寸
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b%\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0013\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006R\u0013\u0010\b\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\t\u0010\u0006R\u0013\u0010\n\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000b\u0010\u0006R\u0013\u0010\f\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\r\u0010\u0006R\u0013\u0010\u000e\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000f\u0010\u0006R\u0013\u0010\u0010\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0011\u0010\u0006R\u0013\u0010\u0012\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0013\u0010\u0006R\u0013\u0010\u0014\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0015\u0010\u0006R\u0013\u0010\u0016\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0017\u0010\u0006R\u0013\u0010\u0018\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0019\u0010\u0006R\u0013\u0010\u001a\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001b\u0010\u0006R\u0013\u0010\u001c\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001d\u0010\u0006R\u0013\u0010\u001e\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001f\u0010\u0006R\u0013\u0010 \u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b!\u0010\u0006R\u0013\u0010\"\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b#\u0010\u0006R\u0013\u0010$\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b%\u0010\u0006\u00a8\u0006&"}, d2 = {"Lcom/example/myapplication/ui/theme/ThemeManager$GameDimensions;", "", "()V", "buttonCornerRadius", "error/NonExistentClass", "getButtonCornerRadius", "()Lerror/NonExistentClass;", "Lerror/NonExistentClass;", "buttonElevation", "getButtonElevation", "cardCornerRadius", "getCardCornerRadius", "cardElevation", "getCardElevation", "cardHeightLarge", "getCardHeightLarge", "cardHeightNormal", "getCardHeightNormal", "cardHeightSmall", "getCardHeightSmall", "cardOverlap", "getCardOverlap", "cardSpacing", "getCardSpacing", "cardWidthLarge", "getCardWidthLarge", "cardWidthNormal", "getCardWidthNormal", "cardWidthSmall", "getCardWidthSmall", "dialogCornerRadius", "getDialogCornerRadius", "dialogElevation", "getDialogElevation", "screenPadding", "getScreenPadding", "sectionSpacing", "getSectionSpacing", "app_debug"})
    public static final class GameDimensions {
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardWidthSmall = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardHeightSmall = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardWidthNormal = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardHeightNormal = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardWidthLarge = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardHeightLarge = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardSpacing = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardOverlap = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass sectionSpacing = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass screenPadding = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardCornerRadius = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass buttonCornerRadius = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass dialogCornerRadius = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardElevation = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass buttonElevation = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass dialogElevation = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.myapplication.ui.theme.ThemeManager.GameDimensions INSTANCE = null;
        
        private GameDimensions() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardWidthSmall() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardHeightSmall() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardWidthNormal() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardHeightNormal() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardWidthLarge() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardHeightLarge() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardSpacing() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardOverlap() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getSectionSpacing() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getScreenPadding() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardCornerRadius() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getButtonCornerRadius() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getDialogCornerRadius() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardElevation() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getButtonElevation() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getDialogElevation() {
            return null;
        }
    }
    
    /**
     * 游戏专用字体大小
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0013\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0013\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006R\u0013\u0010\b\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\t\u0010\u0006R\u0013\u0010\n\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000b\u0010\u0006R\u0013\u0010\f\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\r\u0010\u0006R\u0013\u0010\u000e\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000f\u0010\u0006R\u0013\u0010\u0010\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0011\u0010\u0006R\u0013\u0010\u0012\u001a\u00020\u0004\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0013\u0010\u0006\u00a8\u0006\u0014"}, d2 = {"Lcom/example/myapplication/ui/theme/ThemeManager$GameTypography;", "", "()V", "buttonTextSize", "error/NonExistentClass", "getButtonTextSize", "()Lerror/NonExistentClass;", "Lerror/NonExistentClass;", "cardRankSize", "getCardRankSize", "cardSuitSize", "getCardSuitSize", "hintTextSize", "getHintTextSize", "playerNameSize", "getPlayerNameSize", "scoreSize", "getScoreSize", "titleSize", "getTitleSize", "app_debug"})
    public static final class GameTypography {
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardRankSize = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass cardSuitSize = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass playerNameSize = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass scoreSize = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass titleSize = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass buttonTextSize = null;
        @org.jetbrains.annotations.NotNull()
        private static final error.NonExistentClass hintTextSize = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.myapplication.ui.theme.ThemeManager.GameTypography INSTANCE = null;
        
        private GameTypography() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardRankSize() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getCardSuitSize() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getPlayerNameSize() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getScoreSize() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getTitleSize() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getButtonTextSize() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final error.NonExistentClass getHintTextSize() {
            return null;
        }
    }
}