# 网络问题解决指南

## 问题描述
遇到SSL握手失败和SDK版本兼容性问题。

## 解决方案

### 1. 立即解决方案
```bash
# 使用离线模式构建
./gradlew assembleDebug --offline

# 或者使用提供的批处理文件
build-offline.bat
```

### 2. 网络配置解决方案

#### 方案A: 使用代理
在 `gradle.properties` 中添加：
```properties
systemProp.http.proxyHost=your-proxy-host
systemProp.http.proxyPort=your-proxy-port
systemProp.https.proxyHost=your-proxy-host
systemProp.https.proxyPort=your-proxy-port
```

#### 方案B: 使用国内镜像
在 `build.gradle.kts` 中替换仓库：
```kotlin
repositories {
    maven { url = uri("https://maven.aliyun.com/repository/google") }
    maven { url = uri("https://maven.aliyun.com/repository/central") }
    maven { url = uri("https://maven.aliyun.com/repository/gradle-plugin") }
}
```

#### 方案C: 禁用SSL验证（仅开发环境）

⚠️ **警告**: 此方案仅适用于开发环境，不要在生产环境使用！

**步骤1**: 在 `gradle.properties` 中添加：
```properties
# 禁用SSL验证（仅开发环境）
systemProp.javax.net.ssl.trustStore=NONE
systemProp.javax.net.ssl.trustStoreType=Windows-ROOT
systemProp.javax.net.ssl.trustStorePassword=
systemProp.javax.net.ssl.keyStore=NONE
systemProp.javax.net.ssl.keyStoreType=Windows-ROOT
systemProp.javax.net.ssl.keyStorePassword=
systemProp.com.sun.net.ssl.checkRevocation=false
systemProp.sun.security.ssl.allowUnsafeRenegotiation=true
systemProp.sun.security.ssl.allowLegacyHelloMessages=true
systemProp.javax.net.ssl.HttpsURLConnection.DefaultHostnameVerifier=ALLOW_ALL
```

**步骤2**: 使用专门的构建脚本：
```bash
# 使用禁用SSL的构建脚本
build-no-ssl.bat

# 或手动使用初始化脚本
./gradlew assembleDebug --init-script gradle/init.gradle
```

**步骤3**: 如果仍然失败，设置环境变量：
```cmd
set GRADLE_OPTS=-Djavax.net.ssl.trustStore=NONE -Dcom.sun.net.ssl.checkRevocation=false
./gradlew assembleDebug
```

### 3. SDK版本问题解决

#### 降级到稳定版本
- AGP: 7.4.2
- Kotlin: 1.8.10
- Gradle: 7.6.3
- CompileSdk: 33

#### 使用本地SDK
确保Android Studio SDK Manager中已下载：
- Android SDK Platform 33
- Android SDK Build-Tools 33.0.2
- Android SDK Platform-Tools

### 4. 构建优化

#### 启用离线模式
```bash
# 临时启用
./gradlew build --offline

# 永久启用（在gradle.properties中）
org.gradle.offline=true
```

#### 使用本地缓存
```properties
org.gradle.caching=true
org.gradle.daemon=true
org.gradle.parallel=true
```

### 5. 故障排除步骤

1. **清理项目**
   ```bash
   ./gradlew clean
   ```

2. **检查网络连接**
   ```bash
   ping google.com
   ping maven.google.com
   ```

3. **验证Gradle配置**
   ```bash
   ./gradlew properties
   ```

4. **检查依赖**
   ```bash
   ./gradlew dependencies
   ```

5. **强制刷新依赖**
   ```bash
   ./gradlew build --refresh-dependencies
   ```

### 6. 应急方案

如果所有网络方案都失败，可以：

1. 使用Android Studio的离线模式
2. 从其他机器复制 `.gradle` 缓存目录
3. 使用预编译的依赖包
4. 切换到更稳定的网络环境

### 7. 验证构建成功

构建成功后，APK文件位于：
```
app/build/outputs/apk/debug/app-debug.apk
```

可以通过以下方式验证：
```bash
# 检查APK是否存在
ls -la app/build/outputs/apk/debug/

# 安装到设备
adb install app/build/outputs/apk/debug/app-debug.apk
```
