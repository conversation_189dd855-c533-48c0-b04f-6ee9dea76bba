package com.example.myapplication.data.repository

import android.content.Context
import com.example.myapplication.data.database.*
import com.example.myapplication.data.models.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json

/**
 * 游戏数据仓库
 * 负责管理游戏历史记录和设置的持久化
 */
class GameRepository(context: Context) {
    
    private val database = GameDatabase.getDatabase(context)
    private val gameHistoryDao = database.gameHistoryDao()
    private val gameSettingsDao = database.gameSettingsDao()
    
    /**
     * 保存游戏记录
     */
    suspend fun saveGameHistory(
        gameState: GameState,
        players: List<Player>
    ) {
        val winner = players.getOrNull(gameState.winnerIndex)
        val playerNames = players.map { it.name }
        val playerScores = players.map { it.score }
        
        val historyEntity = GameHistoryEntity(
            gameId = gameState.gameId,
            startTime = gameState.gameStartTime,
            endTime = gameState.gameEndTime,
            duration = gameState.gameDuration,
            result = gameState.result.name,
            winnerName = winner?.name ?: "未知",
            playerNames = Json.encodeToString(playerNames),
            playerScores = Json.encodeToString(playerScores),
            bombCount = gameState.bombCount,
            isSpring = gameState.isSpring,
            landlordIndex = gameState.landlordIndex,
            maxCallingScore = gameState.maxCallingScore
        )
        
        gameHistoryDao.insertGame(historyEntity)
    }
    
    /**
     * 获取所有游戏历史
     */
    suspend fun getAllGameHistory(): List<GameHistoryEntity> {
        return gameHistoryDao.getAllHistory()
    }
    
    /**
     * 获取最近的游戏历史
     */
    suspend fun getRecentGameHistory(limit: Int = 20): List<GameHistoryEntity> {
        return gameHistoryDao.getRecentHistory(limit)
    }
    
    /**
     * 获取游戏统计信息
     */
    suspend fun getGameStatistics(playerName: String): GameStatistics {
        val totalGames = gameHistoryDao.getGameCount()
        val landlordWins = gameHistoryDao.getLandlordWins(playerName)
        val farmerWins = gameHistoryDao.getFarmerWins(playerName)
        val averageDuration = gameHistoryDao.getAverageGameDuration() ?: 0.0
        
        return GameStatistics(
            totalGames = totalGames,
            totalWins = landlordWins + farmerWins,
            landlordWins = landlordWins,
            farmerWins = farmerWins,
            winRate = if (totalGames > 0) (landlordWins + farmerWins).toDouble() / totalGames else 0.0,
            averageGameDuration = averageDuration.toLong()
        )
    }
    
    /**
     * 清空游戏历史
     */
    suspend fun clearGameHistory() {
        gameHistoryDao.clearAllHistory()
    }
    
    /**
     * 删除指定游戏记录
     */
    suspend fun deleteGameHistory(gameId: String) {
        val game = gameHistoryDao.getGameById(gameId)
        if (game != null) {
            gameHistoryDao.deleteGame(game)
        }
    }
    
    /**
     * 获取游戏设置
     */
    suspend fun getGameSettings(): GameSettings {
        val entity = gameSettingsDao.getSettings()
        return if (entity != null) {
            GameSettings(
                playerName = entity.playerName,
                soundEnabled = entity.soundEnabled,
                musicEnabled = entity.musicEnabled,
                aiDifficulty1 = PlayerType.valueOf(entity.aiDifficulty1),
                aiDifficulty2 = PlayerType.valueOf(entity.aiDifficulty2),
                theme = Theme.valueOf(entity.theme),
                animationEnabled = entity.animationEnabled,
                autoPlay = entity.autoPlay,
                gameSpeed = entity.gameSpeed
            )
        } else {
            // 返回默认设置
            getDefaultSettings()
        }
    }
    
    /**
     * 保存游戏设置
     */
    suspend fun saveGameSettings(settings: GameSettings) {
        val entity = GameSettingsEntity(
            playerName = settings.playerName,
            soundEnabled = settings.soundEnabled,
            musicEnabled = settings.musicEnabled,
            aiDifficulty1 = settings.aiDifficulty1.name,
            aiDifficulty2 = settings.aiDifficulty2.name,
            theme = settings.theme.name,
            animationEnabled = settings.animationEnabled,
            autoPlay = settings.autoPlay,
            gameSpeed = settings.gameSpeed
        )
        gameSettingsDao.saveSettings(entity)
    }
    
    /**
     * 更新玩家名称
     */
    suspend fun updatePlayerName(name: String) {
        gameSettingsDao.updatePlayerName(name)
    }
    
    /**
     * 更新音效设置
     */
    suspend fun updateSoundEnabled(enabled: Boolean) {
        gameSettingsDao.updateSoundEnabled(enabled)
    }
    
    /**
     * 更新音乐设置
     */
    suspend fun updateMusicEnabled(enabled: Boolean) {
        gameSettingsDao.updateMusicEnabled(enabled)
    }
    
    /**
     * 更新AI难度
     */
    suspend fun updateAIDifficulty(ai1: PlayerType, ai2: PlayerType) {
        gameSettingsDao.updateAIDifficulty1(ai1.name)
        gameSettingsDao.updateAIDifficulty2(ai2.name)
    }
    
    /**
     * 更新主题
     */
    suspend fun updateTheme(theme: Theme) {
        gameSettingsDao.updateTheme(theme.name)
    }
    
    /**
     * 更新动画设置
     */
    suspend fun updateAnimationEnabled(enabled: Boolean) {
        gameSettingsDao.updateAnimationEnabled(enabled)
    }
    
    /**
     * 更新自动出牌设置
     */
    suspend fun updateAutoPlay(enabled: Boolean) {
        gameSettingsDao.updateAutoPlay(enabled)
    }
    
    /**
     * 更新游戏速度
     */
    suspend fun updateGameSpeed(speed: Int) {
        gameSettingsDao.updateGameSpeed(speed)
    }
    
    /**
     * 获取默认设置
     */
    private fun getDefaultSettings(): GameSettings {
        return GameSettings(
            playerName = "玩家",
            soundEnabled = true,
            musicEnabled = true,
            aiDifficulty1 = PlayerType.AI_MEDIUM,
            aiDifficulty2 = PlayerType.AI_MEDIUM,
            theme = Theme.AUTO,
            animationEnabled = true,
            autoPlay = false,
            gameSpeed = 3
        )
    }
}

/**
 * 游戏统计数据类
 */
data class GameStatistics(
    val totalGames: Int,
    val totalWins: Int,
    val landlordWins: Int,
    val farmerWins: Int,
    val winRate: Double,
    val averageGameDuration: Long
)

/**
 * 游戏设置数据类
 */
data class GameSettings(
    val playerName: String,
    val soundEnabled: Boolean,
    val musicEnabled: Boolean,
    val aiDifficulty1: PlayerType,
    val aiDifficulty2: PlayerType,
    val theme: Theme,
    val animationEnabled: Boolean,
    val autoPlay: Boolean,
    val gameSpeed: Int
)

/**
 * 主题枚举
 */
enum class Theme {
    LIGHT,  // 浅色主题
    DARK,   // 深色主题
    AUTO    // 跟随系统
}
