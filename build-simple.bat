@echo off
chcp 65001 >nul
echo ========================================
echo 简化构建脚本（解决SSL问题）
echo ========================================

echo.
echo 设置环境变量...
set GRADLE_OPTS=-Djavax.net.ssl.trustStore= -Djavax.net.ssl.keyStore= -Dcom.sun.net.ssl.checkRevocation=false -Dorg.gradle.internal.repository.transport.http.allowInsecureProtocol=true

echo.
echo 清理项目...
call gradlew clean --no-daemon --stacktrace

echo.
echo 构建项目...
call gradlew assembleDebug --no-daemon --stacktrace --init-script gradle/init.gradle

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功！
    echo APK位置: app\build\outputs\apk\debug\app-debug.apk
) else (
    echo.
    echo ❌ 构建失败
)

pause
