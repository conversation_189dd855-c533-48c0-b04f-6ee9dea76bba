{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-61:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3bdae484d8629b91b71d2a8e711d605e\\transformed\\material3-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4546,4631,4732,4812,4896,4997,5096,5191,5291,5378,5483,5585,5690,5807,5887,5989", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4541,4626,4727,4807,4891,4992,5091,5186,5286,5373,5478,5580,5685,5802,5882,5984,6083"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1446,1562,1675,1782,1896,1996,2091,2203,2347,2469,2618,2702,2802,2891,2985,3099,3217,3322,3447,3567,3703,3876,4006,4123,4245,4364,4454,4552,4671,4807,4905,5023,5125,5251,5384,5489,5587,5667,5760,5853,5937,6022,6123,6203,6287,6388,6487,6582,6682,6769,6874,6976,7081,7198,7278,7380", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "1557,1670,1777,1891,1991,2086,2198,2342,2464,2613,2697,2797,2886,2980,3094,3212,3317,3442,3562,3698,3871,4001,4118,4240,4359,4449,4547,4666,4802,4900,5018,5120,5246,5379,5484,5582,5662,5755,5848,5932,6017,6118,6198,6282,6383,6482,6577,6677,6764,6869,6971,7076,7193,7273,7375,7474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f11933d2f9c817336fdb6ff83a5031f8\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,971,1055,1125,1199,1271,1342,1420,1487", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,966,1050,1120,1194,1266,1337,1415,1482,1602"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,79,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,924,1004,1099,1198,1280,1357,7479,7568,7650,7731,7815,7885,7959,8031,8203,8281,8348", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "919,999,1094,1193,1275,1352,1441,7563,7645,7726,7810,7880,7954,8026,8097,8276,8343,8463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6dce069ed5f76821f2d9938d1900d2a1\\transformed\\core-1.16.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,80", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,400,498,605,714,8102", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "196,298,395,493,600,709,827,8198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\965fb6c3bf340bea42b1b1c81d253a97\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "84,85", "startColumns": "4,4", "startOffsets": "8468,8558", "endColumns": "89,86", "endOffsets": "8553,8640"}}]}]}