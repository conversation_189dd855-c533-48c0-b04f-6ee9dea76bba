package com.example.myapplication.data.preferences;

/**
 * 设置管理器
 * 使用DataStore管理应用设置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\b\n\u0002\b\u000b\b\u0007\u0018\u0000 >2\u00020\u0001:\u0001>B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u001c\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u000e\u0010\u001e\u001a\u00020\u001fH\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u000e\u0010 \u001a\u00020\u001fH\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u000e\u0010!\u001a\u00020\u001fH\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u001e\u0010\"\u001a\u00020\u001f2\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020$H\u0086@\u00a2\u0006\u0002\u0010&J\u0016\u0010\'\u001a\u00020\u001f2\u0006\u0010(\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010)J\u0016\u0010*\u001a\u00020\u001f2\u0006\u0010(\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010)J\u0016\u0010+\u001a\u00020\u001f2\u0006\u0010,\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010-J\u0016\u0010.\u001a\u00020\u001f2\u0006\u0010,\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010-J\u0016\u0010/\u001a\u00020\u001f2\u0006\u00100\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u00101J\u0016\u00102\u001a\u00020\u001f2\u0006\u00103\u001a\u000204H\u0086@\u00a2\u0006\u0002\u00105J\u0016\u00106\u001a\u00020\u001f2\u0006\u0010(\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010)J\u0016\u00107\u001a\u00020\u001f2\u0006\u00108\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010-J\u0016\u00109\u001a\u00020\u001f2\u0006\u0010(\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010)J\u0016\u0010:\u001a\u00020\u001f2\u0006\u0010;\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010<J\u0016\u0010=\u001a\u00020\u001f2\u0006\u0010(\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010)R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\tR\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\tR\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\tR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\tR\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\tR\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\tR\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\tR\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\t\u00a8\u0006?"}, d2 = {"Lcom/example/myapplication/data/preferences/SettingsManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "animationEnabledFlow", "Lkotlinx/coroutines/flow/Flow;", "", "getAnimationEnabledFlow", "()Lkotlinx/coroutines/flow/Flow;", "gameSettingsFlow", "Lcom/example/myapplication/data/repository/GameSettings;", "getGameSettingsFlow", "isFirstLaunchFlow", "musicEnabledFlow", "getMusicEnabledFlow", "playerNameFlow", "", "getPlayerNameFlow", "soundEnabledFlow", "getSoundEnabledFlow", "themeFlow", "Lcom/example/myapplication/data/repository/Theme;", "getThemeFlow", "tutorialCompletedFlow", "getTutorialCompletedFlow", "vibrationEnabledFlow", "getVibrationEnabledFlow", "getCurrentSettings", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markFirstLaunchCompleted", "", "markTutorialCompleted", "resetToDefaults", "saveAIDifficulty", "ai1", "Lcom/example/myapplication/data/models/PlayerType;", "ai2", "(Lcom/example/myapplication/data/models/PlayerType;Lcom/example/myapplication/data/models/PlayerType;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveAnimationEnabled", "enabled", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveAutoPlay", "saveBackgroundStyle", "style", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveCardStyle", "saveGameSettings", "settings", "(Lcom/example/myapplication/data/repository/GameSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveGameSpeed", "speed", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveMusicEnabled", "savePlayerName", "name", "saveSoundEnabled", "saveTheme", "theme", "(Lcom/example/myapplication/data/repository/Theme;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveVibrationEnabled", "Companion", "app_debug"})
public final class SettingsManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final kotlin.properties.ReadOnlyProperty<?, ?> dataStore$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> PLAYER_NAME = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> SOUND_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> MUSIC_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> AI_DIFFICULTY_1 = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> AI_DIFFICULTY_2 = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> THEME = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> ANIMATION_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> AUTO_PLAY = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Integer> GAME_SPEED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> VIBRATION_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> CARD_STYLE = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> BACKGROUND_STYLE = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> FIRST_LAUNCH = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> TUTORIAL_COMPLETED = null;
    
    /**
     * 获取游戏设置流
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.example.myapplication.data.repository.GameSettings> gameSettingsFlow = null;
    
    /**
     * 获取玩家名称
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<java.lang.String> playerNameFlow = null;
    
    /**
     * 获取音效设置
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<java.lang.Boolean> soundEnabledFlow = null;
    
    /**
     * 获取音乐设置
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<java.lang.Boolean> musicEnabledFlow = null;
    
    /**
     * 获取主题设置
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.example.myapplication.data.repository.Theme> themeFlow = null;
    
    /**
     * 获取动画设置
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<java.lang.Boolean> animationEnabledFlow = null;
    
    /**
     * 获取震动设置
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<java.lang.Boolean> vibrationEnabledFlow = null;
    
    /**
     * 获取是否首次启动
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isFirstLaunchFlow = null;
    
    /**
     * 获取教程是否完成
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<java.lang.Boolean> tutorialCompletedFlow = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.myapplication.data.preferences.SettingsManager.Companion Companion = null;
    
    public SettingsManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 获取游戏设置流
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.example.myapplication.data.repository.GameSettings> getGameSettingsFlow() {
        return null;
    }
    
    /**
     * 获取玩家名称
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.String> getPlayerNameFlow() {
        return null;
    }
    
    /**
     * 获取音效设置
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> getSoundEnabledFlow() {
        return null;
    }
    
    /**
     * 获取音乐设置
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> getMusicEnabledFlow() {
        return null;
    }
    
    /**
     * 获取主题设置
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.example.myapplication.data.repository.Theme> getThemeFlow() {
        return null;
    }
    
    /**
     * 获取动画设置
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> getAnimationEnabledFlow() {
        return null;
    }
    
    /**
     * 获取震动设置
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> getVibrationEnabledFlow() {
        return null;
    }
    
    /**
     * 获取是否首次启动
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isFirstLaunchFlow() {
        return null;
    }
    
    /**
     * 获取教程是否完成
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> getTutorialCompletedFlow() {
        return null;
    }
    
    /**
     * 保存玩家名称
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object savePlayerName(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存音效设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveSoundEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存音乐设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveMusicEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存AI难度设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveAIDifficulty(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType ai1, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType ai2, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存主题设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveTheme(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.repository.Theme theme, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存动画设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveAnimationEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存自动出牌设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveAutoPlay(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存游戏速度
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveGameSpeed(int speed, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存震动设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveVibrationEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存卡牌样式
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveCardStyle(@org.jetbrains.annotations.NotNull()
    java.lang.String style, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存背景样式
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveBackgroundStyle(@org.jetbrains.annotations.NotNull()
    java.lang.String style, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 标记首次启动完成
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object markFirstLaunchCompleted(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 标记教程完成
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object markTutorialCompleted(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 保存完整的游戏设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveGameSettings(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.repository.GameSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 重置所有设置为默认值
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object resetToDefaults(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 获取当前设置的快照
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.myapplication.data.repository.GameSettings> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R%\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00170\u0016*\u00020\u00188BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u001b\u0010\u001c\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006\u001d"}, d2 = {"Lcom/example/myapplication/data/preferences/SettingsManager$Companion;", "", "()V", "AI_DIFFICULTY_1", "Landroidx/datastore/preferences/core/Preferences$Key;", "", "AI_DIFFICULTY_2", "ANIMATION_ENABLED", "", "AUTO_PLAY", "BACKGROUND_STYLE", "CARD_STYLE", "FIRST_LAUNCH", "GAME_SPEED", "", "MUSIC_ENABLED", "PLAYER_NAME", "SOUND_ENABLED", "THEME", "TUTORIAL_COMPLETED", "VIBRATION_ENABLED", "dataStore", "Landroidx/datastore/core/DataStore;", "Landroidx/datastore/preferences/core/Preferences;", "Landroid/content/Context;", "getDataStore", "(Landroid/content/Context;)Landroidx/datastore/core/DataStore;", "dataStore$delegate", "Lkotlin/properties/ReadOnlyProperty;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        private final androidx.datastore.core.DataStore<androidx.datastore.preferences.core.Preferences> getDataStore(android.content.Context $this$dataStore) {
            return null;
        }
    }
}