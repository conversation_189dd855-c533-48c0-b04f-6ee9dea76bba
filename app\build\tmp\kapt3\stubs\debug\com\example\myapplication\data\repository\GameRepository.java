package com.example.myapplication.data.repository;

/**
 * 游戏数据仓库
 * 负责管理游戏历史记录和设置的持久化
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000|\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0011J\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013H\u0086@\u00a2\u0006\u0002\u0010\rJ\b\u0010\u0015\u001a\u00020\u0016H\u0002J\u000e\u0010\u0017\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0011J\u001e\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00140\u00132\b\b\u0002\u0010\u001c\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u0010\u001eJ$\u0010\u001f\u001a\u00020\f2\u0006\u0010 \u001a\u00020!2\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020#0\u0013H\u0086@\u00a2\u0006\u0002\u0010$J\u0016\u0010%\u001a\u00020\f2\u0006\u0010&\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\'J\u001e\u0010(\u001a\u00020\f2\u0006\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020*H\u0086@\u00a2\u0006\u0002\u0010,J\u0016\u0010-\u001a\u00020\f2\u0006\u0010.\u001a\u00020/H\u0086@\u00a2\u0006\u0002\u00100J\u0016\u00101\u001a\u00020\f2\u0006\u0010.\u001a\u00020/H\u0086@\u00a2\u0006\u0002\u00100J\u0016\u00102\u001a\u00020\f2\u0006\u00103\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u00104\u001a\u00020\f2\u0006\u0010.\u001a\u00020/H\u0086@\u00a2\u0006\u0002\u00100J\u0016\u00105\u001a\u00020\f2\u0006\u00106\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0011J\u0016\u00107\u001a\u00020\f2\u0006\u0010.\u001a\u00020/H\u0086@\u00a2\u0006\u0002\u00100J\u0016\u00108\u001a\u00020\f2\u0006\u00109\u001a\u00020:H\u0086@\u00a2\u0006\u0002\u0010;R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006<"}, d2 = {"Lcom/example/myapplication/data/repository/GameRepository;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "database", "Lcom/example/myapplication/data/database/GameDatabase;", "gameHistoryDao", "Lcom/example/myapplication/data/database/GameHistoryDao;", "gameSettingsDao", "Lcom/example/myapplication/data/database/GameSettingsDao;", "clearGameHistory", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteGameHistory", "gameId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllGameHistory", "", "Lcom/example/myapplication/data/database/GameHistoryEntity;", "getDefaultSettings", "Lcom/example/myapplication/data/repository/GameSettings;", "getGameSettings", "getGameStatistics", "Lcom/example/myapplication/data/repository/GameStatistics;", "playerName", "getRecentGameHistory", "limit", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveGameHistory", "gameState", "Lcom/example/myapplication/data/models/GameState;", "players", "Lcom/example/myapplication/data/models/Player;", "(Lcom/example/myapplication/data/models/GameState;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveGameSettings", "settings", "(Lcom/example/myapplication/data/repository/GameSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAIDifficulty", "ai1", "Lcom/example/myapplication/data/models/PlayerType;", "ai2", "(Lcom/example/myapplication/data/models/PlayerType;Lcom/example/myapplication/data/models/PlayerType;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAnimationEnabled", "enabled", "", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAutoPlay", "updateGameSpeed", "speed", "updateMusicEnabled", "updatePlayerName", "name", "updateSoundEnabled", "updateTheme", "theme", "Lcom/example/myapplication/data/repository/Theme;", "(Lcom/example/myapplication/data/repository/Theme;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class GameRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.database.GameDatabase database = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.database.GameHistoryDao gameHistoryDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapplication.data.database.GameSettingsDao gameSettingsDao = null;
    
    public GameRepository(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 保存游戏记录
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveGameHistory(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.GameState gameState, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.myapplication.data.models.Player> players, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 获取所有游戏历史
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllGameHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.myapplication.data.database.GameHistoryEntity>> $completion) {
        return null;
    }
    
    /**
     * 获取最近的游戏历史
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getRecentGameHistory(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.myapplication.data.database.GameHistoryEntity>> $completion) {
        return null;
    }
    
    /**
     * 获取游戏统计信息
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getGameStatistics(@org.jetbrains.annotations.NotNull()
    java.lang.String playerName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.myapplication.data.repository.GameStatistics> $completion) {
        return null;
    }
    
    /**
     * 清空游戏历史
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearGameHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 删除指定游戏记录
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteGameHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String gameId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 获取游戏设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getGameSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.myapplication.data.repository.GameSettings> $completion) {
        return null;
    }
    
    /**
     * 保存游戏设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveGameSettings(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.repository.GameSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新玩家名称
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updatePlayerName(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新音效设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateSoundEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新音乐设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateMusicEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新AI难度
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateAIDifficulty(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType ai1, @org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.models.PlayerType ai2, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新主题
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateTheme(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.repository.Theme theme, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新动画设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateAnimationEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新自动出牌设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateAutoPlay(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新游戏速度
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGameSpeed(int speed, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 获取默认设置
     */
    private final com.example.myapplication.data.repository.GameSettings getDefaultSettings() {
        return null;
    }
}