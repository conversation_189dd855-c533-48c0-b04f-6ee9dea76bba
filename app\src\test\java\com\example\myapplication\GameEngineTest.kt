package com.example.myapplication

import com.example.myapplication.data.models.*
import com.example.myapplication.domain.GameEngine
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before

/**
 * 游戏引擎测试类
 */
class GameEngineTest {
    
    private lateinit var gameEngine: GameEngine
    
    @Before
    fun setup() {
        gameEngine = GameEngine()
    }
    
    @Test
    fun testGameInitialization() = runTest {
        gameEngine.initializeGame("测试玩家", PlayerType.AI_EASY, PlayerType.AI_MEDIUM)
        
        val players = gameEngine.players.value
        val gameState = gameEngine.gameState.value
        
        assertEquals("应该有3个玩家", 3, players.size)
        assertEquals("第一个玩家应该是人类", PlayerType.HUMAN, players[0].type)
        assertEquals("第二个玩家应该是简单AI", PlayerType.AI_EASY, players[1].type)
        assertEquals("第三个玩家应该是中等AI", PlayerType.AI_MEDIUM, players[2].type)
        
        assertEquals("游戏阶段应该是叫地主", GamePhase.CALLING, gameState.phase)
        
        // 检查每个玩家的手牌数量
        players.forEach { player ->
            assertEquals("每个玩家应该有17张牌", 17, player.handSize)
        }
        
        // 检查底牌数量
        assertEquals("应该有3张底牌", 3, gameEngine.landlordCards.value.size)
    }
    
    @Test
    fun testCallLandlord() = runTest {
        gameEngine.initializeGame("测试玩家")
        
        val initialState = gameEngine.gameState.value
        val currentPlayerIndex = initialState.currentPlayerIndex
        
        // 测试叫地主
        val success = gameEngine.callLandlord(currentPlayerIndex, 2)
        assertTrue("叫地主应该成功", success)
        
        val updatedState = gameEngine.gameState.value
        assertEquals("最高叫分应该是2", 2, updatedState.maxCallingScore)
        assertEquals("叫分玩家索引应该正确", currentPlayerIndex, updatedState.callingPlayerIndex)
        
        // 测试无效叫地主（不是当前玩家）
        val invalidSuccess = gameEngine.callLandlord((currentPlayerIndex + 1) % 3, 3)
        assertFalse("非当前玩家叫地主应该失败", invalidSuccess)
    }
    
    @Test
    fun testPlayCards() = runTest {
        gameEngine.initializeGame("测试玩家")
        
        // 模拟叫地主过程，设置地主
        val players = gameEngine.players.value
        val landlordIndex = 0
        val landlord = players[landlordIndex]
        
        // 手动设置地主和游戏状态
        landlord.role = PlayerRole.LANDLORD
        players[1].role = PlayerRole.FARMER
        players[2].role = PlayerRole.FARMER
        
        // 添加底牌给地主
        landlord.addCards(gameEngine.landlordCards.value)
        
        // 设置游戏状态为出牌阶段
        val gameState = gameEngine.gameState.value
        gameState.phase = GamePhase.PLAYING
        gameState.landlordIndex = landlordIndex
        gameState.currentPlayerIndex = landlordIndex
        
        // 测试出单牌
        val singleCard = listOf(landlord.hand.first())
        val success = gameEngine.playCards(landlordIndex, singleCard)
        assertTrue("出单牌应该成功", success)
        
        val updatedState = gameEngine.gameState.value
        assertEquals("最后出牌应该是单牌", singleCard, updatedState.lastPlayedCards)
        assertEquals("最后出牌玩家应该是地主", landlordIndex, updatedState.lastPlayerIndex)
        
        // 测试出无效牌（玩家没有的牌）
        val invalidCard = listOf(Card(Suit.SPADES, Rank.BIG_JOKER))
        val invalidSuccess = gameEngine.playCards(landlordIndex, invalidCard)
        assertFalse("出无效牌应该失败", invalidSuccess)
    }
    
    @Test
    fun testPass() = runTest {
        gameEngine.initializeGame("测试玩家")
        
        // 设置游戏状态
        val gameState = gameEngine.gameState.value
        gameState.phase = GamePhase.PLAYING
        gameState.currentPlayerIndex = 0
        
        // 设置上次出牌，这样才能过牌
        gameState.lastPlayedCards = listOf(Card(Suit.SPADES, Rank.ACE))
        gameState.lastPlayerIndex = 1
        
        val success = gameEngine.pass(0)
        assertTrue("过牌应该成功", success)
        
        val updatedState = gameEngine.gameState.value
        assertEquals("过牌计数应该增加", 1, updatedState.passCount)
    }
    
    @Test
    fun testGetPossiblePlays() = runTest {
        gameEngine.initializeGame("测试玩家")
        
        val players = gameEngine.players.value
        val humanPlayerIndex = 0
        
        // 获取可能的出牌
        val possiblePlays = gameEngine.getPossiblePlays(humanPlayerIndex)
        
        assertFalse("应该有可能的出牌选项", possiblePlays.isEmpty())
        
        // 验证所有可能的出牌都是有效的
        possiblePlays.forEach { cards ->
            assertTrue("玩家应该拥有这些牌", players[humanPlayerIndex].hand.containsAll(cards))
        }
    }
    
    @Test
    fun testGameWinCondition() = runTest {
        gameEngine.initializeGame("测试玩家")
        
        val players = gameEngine.players.value
        val humanPlayer = players[0]
        
        // 模拟玩家出完所有牌
        humanPlayer.hand.clear()
        
        // 设置游戏状态
        val gameState = gameEngine.gameState.value
        gameState.phase = GamePhase.PLAYING
        gameState.currentPlayerIndex = 0
        humanPlayer.role = PlayerRole.LANDLORD
        
        // 模拟最后一次出牌
        val lastCard = listOf(Card(Suit.SPADES, Rank.THREE))
        humanPlayer.hand.add(lastCard[0])
        
        val success = gameEngine.playCards(0, lastCard)
        assertTrue("最后出牌应该成功", success)
        
        val finalState = gameEngine.gameState.value
        assertEquals("游戏应该结束", GamePhase.FINISHED, finalState.phase)
        assertEquals("地主应该获胜", GameResult.LANDLORD_WIN, finalState.result)
        assertEquals("获胜者应该是人类玩家", 0, finalState.winnerIndex)
    }
    
    @Test
    fun testRestartGame() = runTest {
        gameEngine.initializeGame("测试玩家")
        
        // 进行一些游戏操作
        gameEngine.callLandlord(0, 2)
        
        // 重启游戏
        gameEngine.restartGame()
        
        val gameState = gameEngine.gameState.value
        val players = gameEngine.players.value
        
        assertEquals("游戏阶段应该重置为叫地主", GamePhase.CALLING, gameState.phase)
        assertEquals("最高叫分应该重置", 0, gameState.maxCallingScore)
        
        players.forEach { player ->
            assertNull("玩家角色应该重置", player.role)
            assertEquals("玩家得分应该重置", 0, player.score)
            assertFalse("叫地主状态应该重置", player.hasCalledLandlord)
        }
    }
    
    @Test
    fun testGetCurrentPlayer() = runTest {
        gameEngine.initializeGame("测试玩家")
        
        val gameState = gameEngine.gameState.value
        val currentPlayer = gameEngine.getCurrentPlayer()
        val players = gameEngine.players.value
        
        assertNotNull("当前玩家不应该为空", currentPlayer)
        assertEquals("当前玩家应该匹配游戏状态", 
                    players[gameState.currentPlayerIndex], currentPlayer)
    }
    
    @Test
    fun testGetLandlordPlayer() = runTest {
        gameEngine.initializeGame("测试玩家")
        
        // 初始状态下没有地主
        val initialLandlord = gameEngine.getLandlordPlayer()
        assertNull("初始状态下应该没有地主", initialLandlord)
        
        // 设置地主
        val players = gameEngine.players.value
        players[0].role = PlayerRole.LANDLORD
        val gameState = gameEngine.gameState.value
        gameState.landlordIndex = 0
        
        val landlord = gameEngine.getLandlordPlayer()
        assertNotNull("设置后应该有地主", landlord)
        assertEquals("地主应该是第一个玩家", players[0], landlord)
        assertEquals("地主角色应该正确", PlayerRole.LANDLORD, landlord?.role)
    }
}
