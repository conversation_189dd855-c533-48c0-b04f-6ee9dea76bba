package com.example.myapplication

import com.example.myapplication.data.models.*
import com.example.myapplication.domain.AISystem
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before

/**
 * AI系统测试类
 */
class AISystemTest {
    
    private lateinit var aiSystem: AISystem
    
    @Before
    fun setup() {
        aiSystem = AISystem()
    }
    
    @Test
    fun testEasyAICallingDecision() {
        val player = Player.createAI("ai1", "简单AI", PlayerType.AI_EASY, PlayerPosition.LEFT)
        
        // 测试强手牌
        val strongHand = listOf(
            Card(Suit.JOKER, Rank.BIG_JOKER),
            Card(Suit.JOKER, Rank.SMALL_JOKER),
            Card(Suit.SPADES, Rank.TWO),
            Card(Suit.HEARTS, Rank.TWO),
            Card(Suit.CLUBS, Rank.ACE),
            Card(Suit.DIAMONDS, Rank.ACE),
            Card(Suit.SPADES, Rank.KING),
            Card(Suit.HEARTS, Rank.KING),
            Card(Suit.CLUBS, Rank.KING),
            Card(Suit.DIAMONDS, Rank.KING)
        )
        
        val landlordCards = listOf(
            Card(Suit.SPADES, Rank.QUEEN),
            Card(Suit.HEARTS, Rank.QUEEN),
            Card(Suit.CLUBS, Rank.QUEEN)
        )
        
        val decision = aiSystem.makeCallingDecision(player, strongHand, 0, landlordCards)
        assertTrue("强手牌应该叫地主", decision > 0)
        
        // 测试弱手牌
        val weakHand = listOf(
            Card(Suit.SPADES, Rank.THREE),
            Card(Suit.HEARTS, Rank.FOUR),
            Card(Suit.CLUBS, Rank.FIVE),
            Card(Suit.DIAMONDS, Rank.SIX),
            Card(Suit.SPADES, Rank.SEVEN)
        )
        
        val weakDecision = aiSystem.makeCallingDecision(player, weakHand, 0, landlordCards)
        assertEquals("弱手牌不应该叫地主", 0, weakDecision)
    }
    
    @Test
    fun testMediumAICallingDecision() {
        val player = Player.createAI("ai2", "中等AI", PlayerType.AI_MEDIUM, PlayerPosition.RIGHT)
        
        // 测试中等手牌
        val mediumHand = listOf(
            Card(Suit.SPADES, Rank.TWO),
            Card(Suit.HEARTS, Rank.ACE),
            Card(Suit.CLUBS, Rank.KING),
            Card(Suit.DIAMONDS, Rank.QUEEN),
            Card(Suit.SPADES, Rank.JACK),
            Card(Suit.HEARTS, Rank.TEN),
            Card(Suit.CLUBS, Rank.NINE),
            Card(Suit.DIAMONDS, Rank.EIGHT)
        )
        
        val landlordCards = listOf(
            Card(Suit.SPADES, Rank.SEVEN),
            Card(Suit.HEARTS, Rank.SIX),
            Card(Suit.CLUBS, Rank.FIVE)
        )
        
        // 测试不同的最高叫分情况
        val decision1 = aiSystem.makeCallingDecision(player, mediumHand, 0, landlordCards)
        val decision2 = aiSystem.makeCallingDecision(player, mediumHand, 1, landlordCards)
        val decision3 = aiSystem.makeCallingDecision(player, mediumHand, 2, landlordCards)
        
        assertTrue("中等手牌在无人叫地主时应该考虑叫地主", decision1 >= 0)
        assertTrue("随着最高叫分增加，AI应该更谨慎", decision2 <= decision1 + 1)
        assertTrue("高叫分时AI应该更谨慎", decision3 <= decision2 + 1)
    }
    
    @Test
    fun testHardAICallingDecision() {
        val player = Player.createAI("ai3", "困难AI", PlayerType.AI_HARD, PlayerPosition.LEFT)
        
        // 测试有潜力的手牌
        val potentialHand = listOf(
            Card(Suit.SPADES, Rank.ACE),
            Card(Suit.HEARTS, Rank.KING),
            Card(Suit.CLUBS, Rank.QUEEN),
            Card(Suit.DIAMONDS, Rank.JACK),
            Card(Suit.SPADES, Rank.TEN),
            Card(Suit.HEARTS, Rank.NINE),
            Card(Suit.CLUBS, Rank.EIGHT)
        )
        
        // 测试有利的底牌
        val goodLandlordCards = listOf(
            Card(Suit.DIAMONDS, Rank.ACE), // 能组成对子
            Card(Suit.SPADES, Rank.SEVEN), // 能组成顺子
            Card(Suit.JOKER, Rank.SMALL_JOKER) // 王牌
        )
        
        val decision = aiSystem.makeCallingDecision(player, potentialHand, 0, goodLandlordCards)
        assertTrue("困难AI应该考虑底牌价值", decision >= 0)
        
        // 测试不利的底牌
        val badLandlordCards = listOf(
            Card(Suit.SPADES, Rank.THREE),
            Card(Suit.HEARTS, Rank.FOUR),
            Card(Suit.CLUBS, Rank.FIVE)
        )
        
        val badDecision = aiSystem.makeCallingDecision(player, potentialHand, 0, badLandlordCards)
        assertTrue("好底牌应该比坏底牌更容易叫地主", decision >= badDecision)
    }
    
    @Test
    fun testEasyAIPlayDecision() {
        val player = Player.createAI("ai1", "简单AI", PlayerType.AI_EASY, PlayerPosition.LEFT)
        val hand = listOf(
            Card(Suit.SPADES, Rank.THREE),
            Card(Suit.HEARTS, Rank.FOUR),
            Card(Suit.CLUBS, Rank.FIVE),
            Card(Suit.DIAMONDS, Rank.SIX)
        )
        
        val gameState = GameState.newGame()
        val allPlayers = listOf(player)
        
        // 测试首次出牌（没有上家牌型）
        val decision = aiSystem.makePlayDecision(player, hand, null, gameState, allPlayers)
        assertNotNull("简单AI应该能做出出牌决策", decision)
        assertTrue("出牌应该在手牌范围内", hand.containsAll(decision!!))
        
        // 测试跟牌
        val lastPattern = CardPattern.single(Card(Suit.SPADES, Rank.FIVE))
        val followDecision = aiSystem.makePlayDecision(player, hand, lastPattern, gameState, allPlayers)
        
        if (followDecision != null) {
            assertTrue("跟牌应该在手牌范围内", hand.containsAll(followDecision))
        }
        // 如果返回null，说明AI选择过牌，这也是合理的
    }
    
    @Test
    fun testMediumAIPlayDecision() {
        val player = Player.createAI("ai2", "中等AI", PlayerType.AI_MEDIUM, PlayerPosition.RIGHT)
        val hand = listOf(
            Card(Suit.SPADES, Rank.THREE),
            Card(Suit.HEARTS, Rank.FOUR),
            Card(Suit.CLUBS, Rank.KING),
            Card(Suit.DIAMONDS, Rank.ACE),
            Card(Suit.SPADES, Rank.TWO)
        )
        
        val gameState = GameState.newGame()
        val allPlayers = listOf(player)
        
        // 测试优先出小牌的策略
        val decision = aiSystem.makePlayDecision(player, hand, null, gameState, allPlayers)
        assertNotNull("中等AI应该能做出出牌决策", decision)
        
        // 中等AI应该倾向于出较小的牌（当手牌较多时）
        if (decision!!.size == 1) {
            val playedCard = decision[0]
            assertTrue("中等AI应该优先出小牌", 
                      playedCard.weight <= Card(Suit.SPADES, Rank.SEVEN).weight)
        }
    }
    
    @Test
    fun testHardAIPlayDecision() {
        val landlord = Player.createAI("landlord", "地主AI", PlayerType.AI_HARD, PlayerPosition.BOTTOM)
        landlord.role = PlayerRole.LANDLORD
        
        val farmer1 = Player.createAI("farmer1", "农民1", PlayerType.AI_HARD, PlayerPosition.LEFT)
        farmer1.role = PlayerRole.FARMER
        farmer1.hand = mutableListOf(Card(Suit.SPADES, Rank.THREE)) // 只剩1张牌
        
        val farmer2 = Player.createAI("farmer2", "农民2", PlayerType.AI_HARD, PlayerPosition.RIGHT)
        farmer2.role = PlayerRole.FARMER
        
        val allPlayers = listOf(landlord, farmer1, farmer2)
        val gameState = GameState.newGame()
        
        val hand = listOf(
            Card(Suit.SPADES, Rank.FOUR),
            Card(Suit.HEARTS, Rank.FIVE),
            Card(Suit.CLUBS, Rank.KING),
            Card(Suit.DIAMONDS, Rank.ACE)
        )
        
        // 测试地主策略（农民手牌很少时应该出大牌压制）
        val landlordDecision = aiSystem.makePlayDecision(landlord, hand, null, gameState, allPlayers)
        assertNotNull("困难AI地主应该能做出决策", landlordDecision)
        
        // 测试农民策略
        farmer2.hand = hand.toMutableList()
        val farmerDecision = aiSystem.makePlayDecision(farmer2, hand, null, gameState, allPlayers)
        assertNotNull("困难AI农民应该能做出决策", farmerDecision)
    }
    
    @Test
    fun testAIDecisionConsistency() {
        val player = Player.createAI("ai", "测试AI", PlayerType.AI_MEDIUM, PlayerPosition.LEFT)
        val hand = listOf(
            Card(Suit.SPADES, Rank.THREE),
            Card(Suit.HEARTS, Rank.FOUR),
            Card(Suit.CLUBS, Rank.FIVE)
        )
        
        val gameState = GameState.newGame()
        val allPlayers = listOf(player)
        
        // 多次调用应该返回一致的结果类型（虽然具体选择可能不同）
        repeat(5) {
            val decision = aiSystem.makePlayDecision(player, hand, null, gameState, allPlayers)
            if (decision != null) {
                assertTrue("AI决策应该在手牌范围内", hand.containsAll(decision))
                assertFalse("AI不应该出空牌", decision.isEmpty())
            }
        }
    }
    
    @Test
    fun testAIDifficultyDifferences() {
        val easyAI = Player.createAI("easy", "简单AI", PlayerType.AI_EASY, PlayerPosition.LEFT)
        val mediumAI = Player.createAI("medium", "中等AI", PlayerType.AI_MEDIUM, PlayerPosition.LEFT)
        val hardAI = Player.createAI("hard", "困难AI", PlayerType.AI_HARD, PlayerPosition.LEFT)
        
        val testHand = listOf(
            Card(Suit.SPADES, Rank.ACE),
            Card(Suit.HEARTS, Rank.KING),
            Card(Suit.CLUBS, Rank.QUEEN),
            Card(Suit.DIAMONDS, Rank.JACK)
        )
        
        val landlordCards = listOf(
            Card(Suit.SPADES, Rank.TEN),
            Card(Suit.HEARTS, Rank.NINE),
            Card(Suit.CLUBS, Rank.EIGHT)
        )
        
        val easyDecision = aiSystem.makeCallingDecision(easyAI, testHand, 0, landlordCards)
        val mediumDecision = aiSystem.makeCallingDecision(mediumAI, testHand, 0, landlordCards)
        val hardDecision = aiSystem.makeCallingDecision(hardAI, testHand, 0, landlordCards)
        
        // 不同难度的AI可能有不同的决策，但都应该在合理范围内
        assertTrue("简单AI决策应该在合理范围", easyDecision >= 0 && easyDecision <= 3)
        assertTrue("中等AI决策应该在合理范围", mediumDecision >= 0 && mediumDecision <= 3)
        assertTrue("困难AI决策应该在合理范围", hardDecision >= 0 && hardDecision <= 3)
    }
}
