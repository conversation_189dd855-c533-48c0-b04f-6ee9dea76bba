package com.example.myapplication.data.database;

/**
 * 游戏历史DAO
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00070\nH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0018\u0010\u0012\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0013\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u000e\u0010\u0014\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u001c\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u001c\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u0018\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0019J\u0016\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\u001b"}, d2 = {"Lcom/example/myapplication/data/database/GameHistoryDao;", "", "clearAllHistory", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteGame", "game", "Lcom/example/myapplication/data/database/GameHistoryEntity;", "(Lcom/example/myapplication/data/database/GameHistoryEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllHistory", "", "getAverageGameDuration", "", "getFarmerWins", "", "playerName", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getGameById", "gameId", "getGameCount", "getLandlordWins", "getPlayerWins", "getRecentHistory", "limit", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertGame", "app_debug"})
@androidx.room.Dao()
public abstract interface GameHistoryDao {
    
    @androidx.room.Query(value = "SELECT * FROM game_history ORDER BY startTime DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.myapplication.data.database.GameHistoryEntity>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM game_history ORDER BY startTime DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecentHistory(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.myapplication.data.database.GameHistoryEntity>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM game_history WHERE gameId = :gameId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getGameById(@org.jetbrains.annotations.NotNull()
    java.lang.String gameId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.myapplication.data.database.GameHistoryEntity> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertGame(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.database.GameHistoryEntity game, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteGame(@org.jetbrains.annotations.NotNull()
    com.example.myapplication.data.database.GameHistoryEntity game, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM game_history")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearAllHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM game_history")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getGameCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM game_history WHERE result = \'LANDLORD_WIN\' AND winnerName = :playerName")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLandlordWins(@org.jetbrains.annotations.NotNull()
    java.lang.String playerName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM game_history WHERE result = \'FARMERS_WIN\' AND winnerName = :playerName")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFarmerWins(@org.jetbrains.annotations.NotNull()
    java.lang.String playerName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(duration) FROM game_history")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAverageGameDuration(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM game_history WHERE winnerName = :playerName ORDER BY startTime DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPlayerWins(@org.jetbrains.annotations.NotNull()
    java.lang.String playerName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.myapplication.data.database.GameHistoryEntity>> $completion);
}